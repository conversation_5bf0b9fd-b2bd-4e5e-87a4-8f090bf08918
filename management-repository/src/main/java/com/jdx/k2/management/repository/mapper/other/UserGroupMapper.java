package com.jdx.k2.management.repository.mapper.other;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import java.util.List;
import com.jdx.k2.management.domain.po.other.UserGroup;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 用户关联分组mapper类
 *
 * <AUTHOR>
 */
@Mapper
public interface UserGroupMapper extends BaseMapper<UserGroup> {

    @Select("SELECT ug.user_name FROM `group` g " +
            "left join user_group ug on ug.group_number = g.number " +
            "WHERE g.deleted = '1970-01-01 08:00:01' AND ug.deleted = '1970-01-01 08:00:01' AND g.name = #{groupName} ")
    List<String> getUserNameListByGroupName(@Param("groupName") String groupName);
}