package com.jdx.k2.management.repository.mapper.device;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.jdx.k2.management.domain.po.device.HardwareType;
import com.jdx.k2.management.domain.po.device.SensorScheme;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface SensorSchemeMapper extends BaseMapper<SensorScheme> {

    /**
     * 根据车型id联表查询传感器方案信息
     * @param wrapper
     * @return
     */
    @Select("select s.* from sensor_scheme s " +
            "left join device_type_base_info dtbi on dtbi.sensor_scheme_id = s.id " +
            "${ew.customSqlSegment}")
    SensorScheme selectByDeviceTypeId(@Param(Constants.WRAPPER) Wrapper<SensorScheme> wrapper);
}
