package com.jdx.k2.management.repository.mapper.issue;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.jdx.k2.management.domain.bo.issue.IssueIndicatorInfoBO;
import com.jdx.k2.management.domain.po.issue.IssueIndicatorInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface IssueIndicatorInfoMapper extends BaseMapper<IssueIndicatorInfo> {

    @Select("select i.number as number, i.layer_number as layerNumber, i.name as name, i.value as value, " +
            "i.priority as priority, i.issue_pool as issuePool " +
            "from issue_indicator_info i left join issue_indicator_layer_info il on i.layer_number = il.number ${ew.customSqlSegment}")
    List<IssueIndicatorInfoBO> getIssueList(@Param(Constants.WRAPPER) QueryWrapper<IssueIndicatorInfo> issueIndicatorInfoQueryWrapper);
}
