package com.jdx.k2.management.manager.feign;

import com.jdx.k2.management.common.enums.base.BaseResponseEnum;
import com.jdx.k2.management.common.exception.AppRuntimeException;
import com.jdx.rover.common.domain.page.PageDTO;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.datacenter.domain.dto.order.BoxCodeSubOrderDto;
import com.jdx.rover.datacenter.domain.dto.order.NewOrderPlatformDetailDto;
import com.jdx.rover.datacenter.domain.dto.order.NewOrderPlatformPageDto;
import com.jdx.rover.datacenter.domain.dto.order.NewOriginalOrderDto;
import com.jdx.rover.datacenter.domain.dto.order.OrderPlatformQueryEnumDto;
import com.jdx.rover.datacenter.domain.dto.order.PhoneDto;
import com.jdx.rover.datacenter.domain.dto.order.SubOrderDto;
import com.jdx.rover.datacenter.domain.vo.order.NewOrderPlatformPageVo;
import com.jdx.rover.datacenter.domain.vo.order.NewOrderPlatformPhoneVo;
import com.jdx.rover.datacenter.jsf.service.order.NewOrderPlatformService;
import com.jdx.rover.datacenter.jsf.service.order.SubOrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 数据中心feign接口
 * @date 2024年01月26日
 * @version: 1.0
 */
@Slf4j
@Component
public class DatacenterWebFeignManager {

    @Resource
    private NewOrderPlatformService newOrderPlatformService;

    @Resource
    private SubOrderService subOrderService;


    /**
     * @description 获取分页订单列表
     * @param newOrderPlatformPageVo:
     * @return PageDTO<NewOrderPlatformPageDto>
     * <AUTHOR>
     * @date 2024/1/26 15:17
     */
    public PageDTO<NewOrderPlatformPageDto> getOrderPage(NewOrderPlatformPageVo newOrderPlatformPageVo) {
        HttpResult<PageDTO<NewOrderPlatformPageDto>> httpResult = newOrderPlatformService.getOrderPage(newOrderPlatformPageVo);
        if (!HttpResult.isSuccess(httpResult)) {
            log.error("datacenterWebNewOrderPlatformClient.getOrderPage 失败,返回值:{}", JsonUtils.writeValueAsString(httpResult));
            throw new AppRuntimeException(BaseResponseEnum.ERROR_GET_ORDER_HISTORY);
        }
        return httpResult.getData();
    }

    /**
     * @description 获取订单详情
     * @param id:
     * @return NewOrderPlatformDetailDto
     * <AUTHOR>
     * @date 2024/1/26 15:29
     */
    public NewOrderPlatformDetailDto getOrderDetail(Integer id) {
        HttpResult<NewOrderPlatformDetailDto> httpResult = newOrderPlatformService.getOrderDetail(id);
        if (!HttpResult.isSuccess(httpResult)) {
            log.error("datacenterWebNewOrderPlatformClient.getOrderDetail 失败,返回值:{}", JsonUtils.writeValueAsString(httpResult));
            throw new AppRuntimeException(BaseResponseEnum.ERROR_GET_ORDER_DETAIL);
        }
        return httpResult.getData();
    }

    /**
     * @description 获取订单查询枚举
     * @param :
     * @return OrderPlatformQueryEnumDto
     * <AUTHOR>
     * @date 2024/1/26 15:30
     */
    public OrderPlatformQueryEnumDto getOrderQueryEnum() {
        HttpResult<OrderPlatformQueryEnumDto> httpResult = newOrderPlatformService.getOrderQueryEnum();
        if (!HttpResult.isSuccess(httpResult)) {
            log.error("datacenterWebNewOrderPlatformClient.getOrderQueryEnum 失败,返回值:{}", JsonUtils.writeValueAsString(httpResult));
            throw new AppRuntimeException(BaseResponseEnum.ERROR_CALL_SCHEDULE_GET_ORDER_SOURCE_LIST);
        }
        return httpResult.getData();
    }

    /**
     * @description 获取全部订单数量
     * @param newOrderPlatformPageVo:
     * @return NewOriginalOrderDto
     * <AUTHOR>
     * @date 2024/1/26 15:31
     */
    public NewOriginalOrderDto getTotalOriginalOrder(NewOrderPlatformPageVo newOrderPlatformPageVo) {
        HttpResult<NewOriginalOrderDto> httpResult = newOrderPlatformService.getTotalOriginalOrder(newOrderPlatformPageVo);
        if (!HttpResult.isSuccess(httpResult)) {
            log.error("datacenterWebNewOrderPlatformClient.getTotalOriginalOrder 失败,返回值:{}", JsonUtils.writeValueAsString(httpResult));
            throw new AppRuntimeException(BaseResponseEnum.ERROR_GET_ORIGINAL_ORDER_TOTAL);
        }
        return httpResult.getData();
    }

    /**
     * @description 获取手机号
     * @param newOrderPlatformPhoneVo:
     * @return PhoneDto
     * <AUTHOR>
     * @date 2024/1/26 15:27
     */
    public PhoneDto getPhone(NewOrderPlatformPhoneVo newOrderPlatformPhoneVo) {
        HttpResult<PhoneDto> httpResult = newOrderPlatformService.getPhone(newOrderPlatformPhoneVo);
        if (!HttpResult.isSuccess(httpResult)) {
            log.error("datacenterWebNewOrderPlatformClient.getPhone 失败,返回值:{}", JsonUtils.writeValueAsString(httpResult));
            throw new AppRuntimeException(BaseResponseEnum.ERROR_GET_ORDER_HISTORY_PHONE);
        }
        return httpResult.getData();
    }

    /**
     * @description 获取接驳单明细
     * @param batchCode:
     * @return List<SubOrderDto>
     * <AUTHOR>
     * @date 2024/1/26 15:34
     */
    public List<SubOrderDto> getSubOrder(String batchCode) {
        HttpResult<List<SubOrderDto>> httpResult = subOrderService.getSubOrder(batchCode);
        if (!HttpResult.isSuccess(httpResult)) {
            log.error("datacenterWebSubOrderClient.getSubOrder 失败,返回值:{}", JsonUtils.writeValueAsString(httpResult));
            throw new AppRuntimeException(BaseResponseEnum.ERROR_GET_SUB_ORDER);
        }
        return httpResult.getData();
    }

    /**
     * @description 获取箱号明细
     * @param boxCode:
     * @return List<BoxCodeSubOrderDto>
     * <AUTHOR>
     * @date 2024/1/26 15:35
     */
    public List<BoxCodeSubOrderDto> getBoxCodeDetail(String boxCode) {
        HttpResult<List<BoxCodeSubOrderDto>> httpResult = subOrderService.getBoxCodeDetail(boxCode);
        if (!HttpResult.isSuccess(httpResult)) {
            log.error("datacenterWebSubOrderClient.getBoxCodeDetail 失败,返回值:{}", JsonUtils.writeValueAsString(httpResult));
            throw new AppRuntimeException(BaseResponseEnum.ERROR_GET_BOX_CODE);
        }
        return httpResult.getData();
    }

}
