package com.jdx.k2.management.manager.middleware;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.Method;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.jdx.k2.management.common.constants.JdMeConstant;
import com.jdx.k2.management.common.constants.RedisKeyConstant;
import com.jdx.k2.management.domain.dto.technical.bug.AtUserDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 京ME消息卡片manager
 */
@Slf4j
@Component
public class JUECardManager {

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 发送互动卡片消息协议封装
     * @param message 卡片样式
     * @param summary 概要
     * @param groupId 群号
     * @throws Exception
     */
    public void sendJUEMsg(JSONObject message, String summary, String groupId, List<AtUserDTO> atUserDTOList) throws Exception {
        //获取token
        String token = getTeamAccessToken();
        //卡片转发配置
        Map<String, Object> forwardMap = new HashMap<>();
        forwardMap.put("reload", false);
//        forwardMap.put("cardData", message);
//        forwardMap.put("summary", summary); //摘要信息

        //消息体
        Map<String, Object> paramsBody = new HashMap<>();
        paramsBody.put("templateId", JdMeConstant.cardTemplateId); //预配置好的JUE模板ID，从开发者后台创建卡片后获得
        paramsBody.put("templateType", 1); //模板类型 1:普通
        paramsBody.put("summary", summary); //摘要信息
        paramsBody.put("cardData", message); //卡片数据，业务自行定义，接口透传
        paramsBody.put("forward", forwardMap);//卡片转发配置，卡片消息转发时携带此字段，可以处理业务参数
        //At用户
        if (!CollectionUtils.isEmpty(atUserDTOList)) {
            Map<String, Object> atMap = new HashMap<>();
            atMap.put("atAll", false);
            atMap.put("users", atUserDTOList);
            paramsBody.put("at", atMap);
        }
        //发送内容
        Map<String, Object> params = new HashMap<>();
        params.put("robotId", JdMeConstant.id); //机器人ID，创建机器人后获得
        params.put("data", paramsBody); //消息体

        //请求体
        Map<String, Object> body = new HashMap<>();
        body.put("appId", JdMeConstant.appKey); //开放平台注册的应用appkey，创建应用后获得
        body.put("erp", null); //目的地ERP，erp和tenantId是一组信息。目的地要么是erp要么是群号，必须二选一
        body.put("tenantId", "CN.JD.GROUP"); //枚举类型（CN.JD.GROUP，TH.JD.GROUP，ID.JD.GROUP，SF.JD.GROUP），分别为（国内、泰国、印尼、赛夫）租户
        body.put("groupId", groupId); //目的地群号，京ME群号
        body.put("requestId", IdUtil.fastUUID()); //请求UUID，不能重复
        body.put("dateTime", System.currentTimeMillis()); //请求时间戳
        body.put("params", params); //发送内容
        String requestParams = JSONUtil.toJsonStr(body);
        log.info("发送卡片请求:{}", requestParams);

        //构建请求头
        HttpRequest request = new HttpRequest(JdMeConstant.host + JdMeConstant.sendJueMsgURL);
        request.setMethod(Method.POST);
        request.header("Content-Type", "application/json; charset=utf-8");
        request.header("authorization", "Bearer " + token);
        request.header("x-stage", JdMeConstant.env);
        request.body(requestParams);
        log.info("发送互动卡片消息请求：" + request);
        HttpResponse response = request.execute();
        log.info("发送互动卡片消息响应：" + response);
        if(null == response) {
            log.error("调用京ME发送互动消息卡片失败,url:{}", JdMeConstant.sendJueMsgURL);
            throw new RuntimeException("Access interface [" + JdMeConstant.sendJueMsgURL + "] response is null");
        }
        String responseBody = response.body();
        JSONObject responseBodyJson = JSONUtil.parseObj(responseBody);
        int code = responseBodyJson.getInt("code");
        String msg = responseBodyJson.getStr("msg");
        if(0 != code) {
            log.error("调用京ME发送互动消息卡片返回结果失败,responseBody:{}", responseBody);
            throw new RuntimeException("Access interface [" + JdMeConstant.sendJueMsgURL + "] response msg [" + msg + "]");
        }
    }

    /**
     * 获取Team访问TOKEN
     * @return Team Token
     * @throws Exception
     */
    public String getTeamAccessToken() throws Exception {
        String key = RedisKeyConstant.JD_ME_TEAM_TOKEN;
        String teamAccessToken = (String) redisTemplate.opsForValue().get(key);
        if(StrUtil.isNotBlank(teamAccessToken)) {
            log.info("从缓存中获取到Team访问TOKEN：[{}]", teamAccessToken);
            return teamAccessToken;
        }
        String token = getAppAccessToken();
        Map<String, String> params = new HashMap<>();
        params.put("appAccessToken", token);
        params.put("openTeamId", JdMeConstant.openTeamId);
        String requestParams = JSONUtil.toJsonStr(params);

        HttpRequest request = new HttpRequest(JdMeConstant.host + JdMeConstant.teamAccessTokenURL);
        request.setMethod(Method.POST);
        request.header("Content-Type", "application/json; charset=utf-8");
        request.header("x-stage", JdMeConstant.env);
        request.body(requestParams);
        HttpResponse response = request.execute();
        log.info("获取Team访问TOKEN响应：" + response);
        if(null == response) {
            log.error("获取京ME访问凭证失败,URL:{}", JdMeConstant.teamAccessTokenURL);
            throw new RuntimeException("Access interface [" + JdMeConstant.teamAccessTokenURL + "] response is null");
        }
        String responseBody = response.body();
        JSONObject responseBodyJson = JSONUtil.parseObj(responseBody);
        int code = responseBodyJson.getInt("code");
        String msg = responseBodyJson.getStr("msg");
        if(0 != code) {
            log.error("获取京ME访问凭证失败,返回结果:{}", responseBody);
            throw new RuntimeException("Access interface [" + JdMeConstant.teamAccessTokenURL + "] response msg [" + msg + "]");
        }
        JSONObject responseBodyDataJson = responseBodyJson.getJSONObject("data");
        String accessToken = responseBodyDataJson.getStr("teamAccessToken");
        int expireIn = responseBodyDataJson.getInt("expireIn");
        redisTemplate.opsForValue().set(key, accessToken, expireIn - 10 * 60, TimeUnit.SECONDS);
        return accessToken;
    }

    /**
     * 获取APP访问TOKEN
     * @return app token
     * @throws Exception
     */
    public String getAppAccessToken() throws Exception {
        String key = RedisKeyConstant.JD_ME_APP_TOKEN;
        String appAccessToken = (String) redisTemplate.opsForValue().get(key);
        if(StrUtil.isNotBlank(appAccessToken)) {
            log.info("从缓存中获取到APP访问TOKEN：[{}]",  appAccessToken);
            return appAccessToken;
        }

        Map<String, String> params = new HashMap<>();
        params.put("appKey", JdMeConstant.appKey);
        params.put("appSecret", JdMeConstant.appSecret);
        String requestParams = JSONUtil.toJsonStr(params);

        HttpRequest request = new HttpRequest(JdMeConstant.host + JdMeConstant.appAccessTokenURL);
        request.setMethod(Method.POST);
        request.header("Content-Type", "application/json; charset=utf-8");
        request.header("x-stage", JdMeConstant.env);
        request.body(requestParams);
        HttpResponse response = request.execute();
        log.info("获取APP访问TOKEN响应：" + response);
        if(null == response) {
            log.error("获取APP访问TOKEN响应为空,URL:{}", JdMeConstant.appAccessTokenURL);
            throw new RuntimeException("Access interface [" + JdMeConstant.appAccessTokenURL + "] response is null");
        }
        String responseBody = response.body();
        JSONObject responseBodyJson = JSONUtil.parseObj(responseBody);
        int code = responseBodyJson.getInt("code");
        String msg = responseBodyJson.getStr("msg");
        if(0 != code) {
            log.error("获取APP访问TOKEN响应失败,响应结果:{}", responseBody);
            throw new RuntimeException("Access interface [" + JdMeConstant.appAccessTokenURL + "] response msg [" + msg + "]");
        }
        JSONObject responseBodyDataJson = responseBodyJson.getJSONObject("data");
        String accessToken = responseBodyDataJson.getStr("appAccessToken");
        int expireIn = responseBodyDataJson.getInt("expireIn");
        redisTemplate.opsForValue().set(key, accessToken, expireIn - 10 * 60, TimeUnit.SECONDS);
        return accessToken;
    }
}
