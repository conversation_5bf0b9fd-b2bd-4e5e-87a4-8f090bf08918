package com.jdx.k2.management.manager.business;

import com.jdx.k2.management.common.exception.AppRuntimeException;
import com.jdx.rover.common.utils.result.HttpResult;
import java.util.List;

import com.jdx.rover.schedule.jsf.schedule.ScheduleMetadataVehicleScheduleJsfService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023/3/31 14:52
 * @description
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class VehicleScheduleManager {

    private final ScheduleMetadataVehicleScheduleJsfService scheduleMetadataVehicleScheduleJsfService;

    public List<String> haveScheduleVehicle(List<String> vehicleNameList) {
        HttpResult<List<String>> httpResult = scheduleMetadataVehicleScheduleJsfService.checkVehicleSchedules(vehicleNameList);
        if (HttpResult.isSuccess(httpResult)) {
            return httpResult.getData();
        }
        log.error(">>>>>>调用【车辆调度】，判断是否有调度接口失败！<<<<<<");
        throw new AppRuntimeException("【车辆调度】服务异常！");
    }
}
