package com.jdx.k2.management.manager.jmq;

import com.jdx.k2.management.domain.po.cockpit.CockpitInfo;
import com.jdx.k2.management.domain.po.cockpit.CockpitTeamInfo;
import com.jdx.rover.metadata.api.domain.dto.kafka.CommonMessageDTO;
import com.jdx.rover.metadata.api.domain.enums.kafka.OperationTypeEnum;
import com.jdx.rover.metadata.api.domain.enums.kafka.SubjectEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import java.util.HashMap;

/**
 * <AUTHOR>
 * @description: 无人车停靠点相关消息
 * @date 2024年12月06日
 * @version: 1.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class JmqCockpitProducer {

    private final JmqCommonProducer jmqCommonProducer;

    @Value("${jmq.topic.producer.metadata_cockpit_vehicle_message}")
    private String cockpitVehicleMessageTopic;

    @Value("${jmq.topic.producer.metadata_cockpit_team_message}")
    private String cockpitTeamMessageTopic;


    /**
     * @description 发送驾舱车辆消息
     * @param cockpitInfo:
     * @param operationTypeEnum:
     * @return void
     * <AUTHOR>
     * @date 2024/12/6 16:33
     */
    @Async("asyncThreadPool")
    public void sendCockpitVehicleMessage(CockpitInfo cockpitInfo, OperationTypeEnum operationTypeEnum) {
        if (ObjectUtils.isEmpty(cockpitInfo)) {
            return;
        }
        //构建消息体
        CommonMessageDTO commonMessageDTO = jmqCommonProducer.buildMessageData(SubjectEnum.COCKPIT_VEHICLE, operationTypeEnum, cockpitInfo.getId(), cockpitInfo.getNumber(), new HashMap<>());
        //消息发送
        log.info("发送驾舱消息");
        jmqCommonProducer.sendMassage(cockpitVehicleMessageTopic, commonMessageDTO);
    }

    /**
     * @description 发送驾舱团队消息
     * @param cockpitTeamInfo:
     * @param operationTypeEnum:
     * @return void
     * <AUTHOR>
     * @date 2024/12/6 16:33
     */
    @Async("asyncThreadPool")
    public void sendCockpitTeamMessage(CockpitTeamInfo cockpitTeamInfo, OperationTypeEnum operationTypeEnum) {
        if (ObjectUtils.isEmpty(cockpitTeamInfo)) {
            return;
        }
        //构建消息体
        CommonMessageDTO commonMessageDTO = jmqCommonProducer.buildMessageData(SubjectEnum.COCKPIT_TEAM_INFO, operationTypeEnum, cockpitTeamInfo.getId(), cockpitTeamInfo.getNumber(), new HashMap<>());
        //消息发送
        log.info("发送驾舱消息");
        jmqCommonProducer.sendMassage(cockpitTeamMessageTopic, commonMessageDTO);
    }

}
