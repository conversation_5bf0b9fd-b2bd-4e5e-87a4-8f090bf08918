package com.jdx.k2.management.manager.jmq;

import com.jdx.rover.metadata.api.domain.message.RequireInfoMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @description: 无人车维修单相关消息
 * @date 2024年12月06日
 * @version: 1.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class JmqRequireProducer {

    private final JmqCommonProducer jmqCommonProducer;

    @Value("${jmq.topic.producer.metadata_vehicle_require_info}")
    private String vehicleRequireMessageTopic;


    /**
     * @description 发送维修单变化消息
     * @param requireInfoMessage:
     * @return void
     * <AUTHOR>
     * @date 2024/12/6 16:35
     */
    @Async("asyncThreadPool")
    public void sendRequireInfoMessage(RequireInfoMessage requireInfoMessage) {
        jmqCommonProducer.sendMassage(vehicleRequireMessageTopic, requireInfoMessage);
    }
}
