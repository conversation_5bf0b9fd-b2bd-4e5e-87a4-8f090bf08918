package com.jdx.k2.management.manager.business;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.jdx.k2.indoor.map.jsf.domain.dto.mapInfo.PointInfoDTO;
import com.jdx.k2.indoor.map.jsf.domain.enums.MapPointTypeEnum;
import com.jdx.k2.indoor.map.jsf.domain.vo.mapInfo.PointInfoListOfNewestMapGetVO;
import com.jdx.k2.management.domain.bo.RobotDeviceInfoBO;
import com.jdx.k2.management.domain.po.device.DeviceBaseInfo;
import com.jdx.k2.management.domain.po.device.RobotInfo;
import com.jdx.k2.management.manager.feign.IndoorMapJsfManager;
import com.jdx.k2.management.repository.mapper.device.DeviceBaseInfoMapper;
import com.jdx.k2.management.repository.mapper.device.RobotInfoMapper;
import com.jdx.k2.management.repository.mapper.station.StationBaseInfoMapper;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.jsf.util.JsfLoginUtil;
import com.jdx.rover.metadata.api.domain.enums.VehicleBusinessTypeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import static cn.hutool.core.date.DateTime.now;

/**
 * @description: 机器人信息Manager
 * @author: douyanghui
 * @date: 2024/12/24
 */

@Component
@Slf4j
@RequiredArgsConstructor
public class RobotInfoManager {

    /**
     * 机器人信息mapper
     */
    private final RobotInfoMapper robotInfoMapper;

    /**
     * 站点基础信息mapper
     */
    private final StationBaseInfoMapper stationBaseInfoMapper;

    /**
     * 地图信息jsf manager
     */
    private final IndoorMapJsfManager indoorMapJsfManager;

    /**
     * 设备基础信息mapper
     */
    private final DeviceBaseInfoMapper deviceBaseInfoMapper;

    /**
     * 绑定机器人设备的停靠点（多合一机器人的泊车点）
     *
     * @param number 站点业务编号
     * @param deviceIdList 设备id列表
     */
    @Transactional(rollbackFor = Exception.class)
    public void bindDeviceParkStop(Integer stationBaseId, String number, List<Integer> deviceIdList) {
        String username = JsfLoginUtil.getUsername();
        if (StringUtils.isBlank(number) || CollectionUtils.isEmpty(deviceIdList)) {
            return;
        }

        //根据设备id列表获取设备信息列表
        LambdaQueryWrapper<DeviceBaseInfo> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(DeviceBaseInfo::getId, deviceIdList);
        List<RobotDeviceInfoBO> robotDeviceInfoBOList = deviceBaseInfoMapper.selectRobotDeviceInfoListByParams(lambdaQueryWrapper);
        if (CollectionUtils.isEmpty(robotDeviceInfoBOList)) {
            return;
        }

        //获取站点下最新的地图泊车点信息
        List<PointInfoDTO> pointInfoDTOList = getMapPointList(number);
        if (CollectionUtils.isEmpty(pointInfoDTOList)) {
            return;
        }

        //查询已绑定的泊车点
        List<String> parkStopNoList = robotInfoMapper.selectParkStopListByStationId(stationBaseId);
        if(!CollectionUtils.isEmpty(parkStopNoList)) {
            //过滤已绑定的泊车点
            pointInfoDTOList = pointInfoDTOList.stream().filter(pointInfoDTO -> !parkStopNoList.contains(pointInfoDTO.getPointNo())).collect(Collectors.toList());
        }
        log.info("bindDeviceParkStop===>获取站点下未绑定车辆的泊车点:{}", JsonUtils.writeValueAsString(pointInfoDTOList));
        //给设备绑定泊车点
        List<PointInfoDTO> finalPointInfoDTOList = pointInfoDTOList;
        List<RobotInfo> robotInfoList = robotDeviceInfoBOList.stream().map(robotDeviceInfoBO -> {
            RobotInfo robotInfo = new RobotInfo();
            robotInfo.setDeviceBaseId(robotDeviceInfoBO.getDeviceBaseId());
            robotInfo.setModifyUser(username);
            robotInfo.setModifyTime(now());

            String businessType = robotDeviceInfoBO.getBusinessType();
            PointInfoDTO homePoint = getHomePoint(finalPointInfoDTOList, businessType);
            if (homePoint != null) {
                robotInfo.setHomePoi(homePoint.getPointNo());
                finalPointInfoDTOList.remove(homePoint);
            }
            return robotInfo;
        }).collect(Collectors.toList());

        log.info("bindDeviceParkStop===>自动绑定泊车点:{}", JsonUtils.writeValueAsString(robotInfoList));
        //更新设备信息
        robotInfoMapper.updateBatchById(robotInfoList);
    }

    /**
     * 根据业务类型获取泊车点
     * @param pointInfoDTOList 地图点位信息列表
     * @param businessType 业务类型
     * @return 泊车点
     */
    private PointInfoDTO getHomePoint(List<PointInfoDTO> pointInfoDTOList, String businessType) {
        return pointInfoDTOList.stream()
                .filter(point -> point.getType().equals(getMapPointType(businessType)))
                .findFirst()
                .orElse(null);
    }

    /**
     * 根据业务类型获取地图点位类型
     * @param businessType 业务类型
     * @return 点位类型
     */
    private String getMapPointType(String businessType) {
        if(businessType.equals(VehicleBusinessTypeEnum.PUT_IN_WAREHOUSE.getValue())){
            return MapPointTypeEnum.PUT_IN_WAREHOUSE_PARK_POINT.getValue();
        }else if(businessType.equals(VehicleBusinessTypeEnum.OUT_WAREHOUSE.getValue()) || businessType.equals(VehicleBusinessTypeEnum.FLOW_OUT_WAREHOUSE.getValue())
        || businessType.equals(VehicleBusinessTypeEnum.COMMON_WAREHOUSE.getValue())){
            //出库、边拣边分、通用底盘都设置车出库类型
            return MapPointTypeEnum.OUT_WAREHOUSE_PARK_POINT.getValue();
        }
        return "";
    }


    /**
     * 获取地图点位信息列表
     * @param stationNumber 站点编号
     * @return 点位信息
     */
    public List<PointInfoDTO> getMapPointList(String stationNumber) {
        if (Objects.isNull(stationNumber)) {
            return null;
        }
        PointInfoListOfNewestMapGetVO pointInfoListOfNewestMapGetVO = new PointInfoListOfNewestMapGetVO();
        pointInfoListOfNewestMapGetVO.setWarehouseNo(stationNumber);
        //获取站点下最新的地图泊车点信息
        return indoorMapJsfManager.getPointInfoList(pointInfoListOfNewestMapGetVO);
    }
}
