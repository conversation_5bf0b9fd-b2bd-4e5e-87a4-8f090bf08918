/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.k2.management.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * 动态获取站点信息枚举
 */
@Getter
@ToString
@AllArgsConstructor
public enum StationInfoTypeEnum {
    SINGLE("SINGLE", "单车绑定跨站停靠点"),
    BATCH("BATCH", "多车绑定跨站停靠点"),
    ;

    private final String value;

    private final String name;

    public static String getNameByValue(String value) {
        StationInfoTypeEnum[] var1 = values();
        for (StationInfoTypeEnum stationInfoTypeEnum : var1) {
            if (stationInfoTypeEnum.getValue().equals(value)) {
                return stationInfoTypeEnum.getName();
            }
        }
        return null;
    }
}
