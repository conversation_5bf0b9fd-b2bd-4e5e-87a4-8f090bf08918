/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.k2.management.common.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

@Getter
@ToString
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public enum EventRuleTypeEnum {
    ALARM("ALARM", "告警"),
    EXCEPTION("EXCEPTION", "异常"),
    BUSINESS("BUSINESS", "业务"),
    ;

    private final String value;

    private final String name;

    /**
     * 获取枚举
     */
    public static EventRuleTypeEnum getByValue(String value) {
        for (EventRuleTypeEnum ruleTypeEnum : EventRuleTypeEnum.values()) {
            if (ruleTypeEnum.getValue().equals(value)) {
                return ruleTypeEnum;
            }
        }
        return null;
    }

}
