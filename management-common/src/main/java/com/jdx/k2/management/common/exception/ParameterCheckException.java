/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.k2.management.common.exception;

import com.jdx.k2.management.common.enums.base.BaseResponseEnum;
import lombok.Getter;

/**
 * 参数校验统一异常
 *
 * <AUTHOR>
 * @version 1.0
 */
public class ParameterCheckException extends RuntimeException {

    @Getter
    private final String code;

    /**
     * 构造函数
     */
    public ParameterCheckException(BaseResponseEnum baseResponseEnum) {
        super(baseResponseEnum.getMessage());
        this.code = baseResponseEnum.getCode();
    }
}