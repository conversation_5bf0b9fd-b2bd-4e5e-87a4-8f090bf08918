package com.jdx.k2.management.common.utils;

/**
 * <AUTHOR>
 * @description: 坐标转换工具方法
 * @date 2025年03月12日
 * @version: 1.0
 */
public class GPSCoordinateUtils {

    private static final double PI = 3.1415926535897932384626;
    private static final double A = 6378245.0;  // 长半轴
    private static final double EE = 0.00669342162296594323;  // 扁率

    // 判断是否在中国境内
    public static boolean isOutOfChina(double lat, double lon) {
        if (lon < 72.004 || lon > 137.8347)
            return true;
        if (lat < 0.8293 || lat > 55.8271)
            return true;
        return false;
    }

    // WGS-84转GCJ-02
    public static double[] wgs84ToGcj02(double lat, double lon) {
        if (isOutOfChina(lat, lon)) {
            return new double[]{lat, lon};
        }
        double dLat = transformLat(lon - 105.0, lat - 35.0);
        double dLon = transformLon(lon - 105.0, lat - 35.0);
        double radLat = lat / 180.0 * PI;
        double magic = Math.sin(radLat);
        magic = 1 - EE * magic * magic;
        double sqrtMagic = Math.sqrt(magic);
        dLat = (dLat * 180.0) / ((A * (1 - EE)) / (magic * sqrtMagic) * PI);
        dLon = (dLon * 180.0) / (A / sqrtMagic * Math.cos(radLat) * PI);
        double mgLat = lat + dLat;
        double mgLon = lon + dLon;
        return new double[]{mgLat, mgLon};
    }

    // GCJ-02转WGS-84
    public static double[] gcj02ToWgs84(double lat, double lon) {
        double[] gcj02 = wgs84ToGcj02(lat, lon);
        double dLat = lat * 2 - gcj02[0];
        double dLon = lon * 2 - gcj02[1];
        return new double[]{dLat, dLon};
    }

    // CGCS2000与WGS-84转换 (通常认为它们几乎相同)
    public static double[] cgcs2000ToWgs84(double lat, double lon) {
        // 简单转换，认为CGCS2000与WGS-84基本一致
        return new double[]{lat, lon};
    }

    // WGS-84转CGCS2000
    public static double[] wgs84ToCgcs2000(double lat, double lon) {
        // 简单转换，认为WGS-84与CGCS2000基本一致
        return new double[]{lat, lon};
    }

    // CGCS2000转GCJ-02
    public static double[] cgcs2000ToGcj02(double lat, double lon) {
        // 第一步：将CGCS2000坐标转换为WGS-84
        double[] wgs84Coords = cgcs2000ToWgs84(lat, lon);
        // 第二步：将WGS-84转换为GCJ-02
        return wgs84ToGcj02(wgs84Coords[0], wgs84Coords[1]);
    }

    // GCJ-02转CGCS2000
    public static double[] gcj02ToCgcs2000(double lat, double lon) {
        // 第一步：将GCJ-02转换为WGS-84
        double[] wgs84Coords = gcj02ToWgs84(lat, lon);
        // 第二步：将WGS-84转换为CGCS2000
        return wgs84ToCgcs2000(wgs84Coords[0], wgs84Coords[1]);
    }

    // 转换纬度
    private static double transformLat(double x, double y) {
        double ret = -100.0 + 2.0 * x + 3.0 * y + 0.2 * y * y + 0.1 * x * y + 0.2 * Math.sqrt(Math.abs(x));
        ret += (20.0 * Math.sin(6.0 * x * PI) + 20.0 * Math.sin(2.0 * x * PI)) * 2.0 / 3.0;
        ret += (20.0 * Math.sin(y * PI) + 40.0 * Math.sin(y / 3.0 * PI)) * 2.0 / 3.0;
        ret += (160.0 * Math.sin(y / 12.0 * PI) + 320 * Math.sin(y * PI / 30.0)) * 2.0 / 3.0;
        return ret;
    }

    // 转换经度
    private static double transformLon(double x, double y) {
        double ret = 300.0 + x + 2.0 * y + 0.1 * x * x + 0.1 * x * y + 0.1 * Math.sqrt(Math.abs(x));
        ret += (20.0 * Math.sin(6.0 * x * PI) + 20.0 * Math.sin(2.0 * x * PI)) * 2.0 / 3.0;
        ret += (20.0 * Math.sin(x * PI) + 40.0 * Math.sin(x / 3.0 * PI)) * 2.0 / 3.0;
        ret += (150.0 * Math.sin(x / 12.0 * PI) + 300.0 * Math.sin(x / 30.0 * PI)) * 2.0 / 3.0;
        return ret;
    }
}
