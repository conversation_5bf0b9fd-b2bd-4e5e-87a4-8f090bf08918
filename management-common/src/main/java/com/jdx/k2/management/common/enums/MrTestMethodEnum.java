package com.jdx.k2.management.common.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * MR测试方式枚举
 */
@Getter
@ToString
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public enum MrTestMethodEnum {

    ONLY_SIMULATION("ONLY_SIMULATION", "仅仿真"),
    ONLY_REAL_VEHICLE("ONLY_REAL_VEHICLE", "仅实车"),
    SIMULATION_REAL_VEHICLE("SIMULATION_REAL_VEHICLE", "仿真和实车均测试"),
    ;

    private final String value;

    private final String name;

    /**
     * 获取枚举
     */
    public static String getNameByValue(String value) {
        for (MrTestMethodEnum mrTestMethodEnum : MrTestMethodEnum.values()) {
            if (mrTestMethodEnum.getValue().equals(value)) {
                return mrTestMethodEnum.getName();
            }
        }
        return null;
    }
}
