/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.k2.management.common.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

@AllArgsConstructor(access = AccessLevel.PRIVATE)
@ToString
@Getter
public enum RequireResourceTypeEnum {

    PICTURE("PICTURE", "照片"),
    VIDEO("VIDEO", "视频"),
    ;

    private String value;

    private String name;

    /**
     * 获取名称
     */
    public static String getNameByValue(String value) {
        for (RequireResourceTypeEnum requireResourceTypeEnum : RequireResourceTypeEnum.values()) {
            if (requireResourceTypeEnum.getValue().equals(value)) {
                return requireResourceTypeEnum.getName();
            }
        }
        return null;
    }
}
