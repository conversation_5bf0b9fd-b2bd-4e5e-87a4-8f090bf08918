/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.k2.management.common.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

@AllArgsConstructor(access = AccessLevel.PRIVATE)
@ToString
@Getter
public enum RequireStatusEnum {

    TO_DO_ACCEPT(1, "待受理"),
    ACCEPTED(2, "已受理"),
    CAN_NOT_ACCEPT(3, "不受理"),
    MAINTAINING(4, "维修中"),
    TO_DO_CHECK(5, "待确认"),
    COMPLETE(6, "已完成");

    /**
     * 值
     */
    private final Integer value;

    /**
     * 名称
     */
    private final String name;

    /**
     * 获取名称
     */
    public static String getNameByValue(Integer value) {
        for (RequireStatusEnum requireStatusEnum : RequireStatusEnum.values()) {
            if (requireStatusEnum.getValue().equals(value)) {
                return requireStatusEnum.getName();
            }
        }
        return null;
    }
}
