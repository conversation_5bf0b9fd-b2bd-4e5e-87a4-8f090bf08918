package com.jdx.k2.management.common.constants;

public class BugConstants {

    /**
     * 行云链接
     */
    public static final String url = "http://xingyun.jd.com/test-manage/jbug/";
    /**
     * 行云BUG编号前缀
     */
    public static final String BUG_PREFIX = "B";
    /**
     * 当前操作人erp
     */
    public static final String optErp = "org.jdx.operation1";

    /**
     * 分隔符
     */
    public static final String SEPARATOR = ";";

    /**
     * 间隔符
     */
    public static final String DELIMITER = "-";

    /**
     * 行云缺陷列表查询分页量
     */
    public static final Long LIMIT = 50L;

    /**
     * 仿真系统任务不存在返回值
     */
    public static final Long SIMULATION_TASK_NO_EXIST = 0L;

    /**
     * 亮灯天数起始值
     */
    public static final Integer ALARM_DAYS = 1;

    /**
     * 行云缺陷优先级PO
     */
    public static final String bug_priority_P0 = "P0";

    /**
     * 行云缺陷优先级P1
     */
    public static final String bug_priority_P1 = "P1";

    /**
     * 行云缺陷优先级P2
     */
    public static final String bug_priority_P2 = "P2";

    /**
     * P0亮红灯,宽限天数
     */
    public static final Integer BUG_P0_RED_GRACE_DAYS = 2;

    /**
     * P1亮红灯,宽限天数
     */
    public static final Integer BUG_P1_RED_GRACE_DAYS = 8;

    /**
     * P1亮黄灯,宽限天数
     */
    public static final Integer BUG_P1_YELLOW_GRACE_DAYS = 3;

    /**
     * P2亮黄灯,宽限天数
     */
    public static final Integer BUG_P2_YELLOW_GRACE_DAYS = 4;

    /**
     * 0
     */
    public static final Integer BUG_ZERO = 0;
    /**
     * typeHandler
     */
    public static final String typeHandler = "typeHandler=";

    /**
     * 仿真包跳转链接
     */
    public static final String SIMULATION_RECORD_URL = "http://sim-analysis.jdl.com/#/web/scene/detail?fileName=";
}
