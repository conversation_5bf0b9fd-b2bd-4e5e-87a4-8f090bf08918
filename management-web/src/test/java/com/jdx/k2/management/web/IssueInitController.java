package com.jdx.k2.management.web;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.read.listener.PageReadListener;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jdx.k2.management.common.constants.Constants;
import com.jdx.k2.management.common.utils.NumberGeneratorUtil;
import com.jdx.k2.management.domain.po.issue.IssueIndicatorInfo;
import com.jdx.k2.management.domain.po.issue.IssueIndicatorLayerInfo;
import com.jdx.k2.management.repository.mapper.issue.IssueIndicatorInfoMapper;
import com.jdx.k2.management.repository.mapper.issue.IssueIndicatorLayerInfoMapper;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

/**
 * 工单初始化Controller
 */
public class IssueInitController extends BaseTest{

    @Resource
    private IssueIndicatorInfoMapper issueIndicatorInfoMapper;

    @Resource
    private IssueIndicatorLayerInfoMapper issueIndicatorLayerInfoMapper;


    @Test
    public void initIssueLayer() {
        String fileName = "/Users/<USER>/Desktop/issueInit/init.xlsx";
        // 读取 Excel 文件
        EasyExcel.read(fileName, IssueLayerDTO.class, new PageReadListener<IssueLayerDTO>(dataList -> {
            for (IssueLayerDTO issueLayerDTO : dataList) {
                IssueIndicatorLayerInfo issueIndicatorLayerInfo = new IssueIndicatorLayerInfo();
                issueIndicatorLayerInfo.setName(issueLayerDTO.getName());
                issueIndicatorLayerInfo.setValue(issueLayerDTO.getValue());
                issueIndicatorLayerInfo.setPriority(issueLayerDTO.getPriority());
                issueIndicatorLayerInfo.setNumber(NumberGeneratorUtil.generateNumberConcurrent("A"));
                issueIndicatorLayerInfo.setCreateUser(Constants.SYSTEM);
                issueIndicatorLayerInfo.setModifyUser(Constants.SYSTEM);
                issueIndicatorLayerInfoMapper.insert(issueIndicatorLayerInfo);
            }
        })).sheet().doRead();


    }

    @Test
    public void initIssue() {
        String fileName = "/Users/<USER>/Desktop/issueInit/init.xlsx";
        // 读取 sheet2 文件
        EasyExcel.read(fileName, IssueDTO.class, new PageReadListener<IssueDTO>(dataList -> {
            for (IssueDTO issueDTO : dataList) {
                System.out.println(issueDTO);
                IssueIndicatorInfo issueIndicatorInfo = new IssueIndicatorInfo();
                issueIndicatorInfo.setName(issueDTO.getName());
                issueIndicatorInfo.setValue(issueDTO.getValue());
                issueIndicatorInfo.setPriority(issueDTO.getPriority());
                issueIndicatorInfo.setNumber(NumberGeneratorUtil.generateNumberConcurrent("B"));
                issueIndicatorInfo.setIssuePool(issueDTO.getIssuePool());
                issueIndicatorInfo.setCreateUser(Constants.SYSTEM);
                issueIndicatorInfo.setModifyUser(Constants.SYSTEM);

                LambdaQueryWrapper<IssueIndicatorLayerInfo> issueIndicatorLayerInfoLambdaQueryWrapper = new LambdaQueryWrapper<>();
                issueIndicatorLayerInfoLambdaQueryWrapper.eq(IssueIndicatorLayerInfo::getValue, issueDTO.getLayerValue());
                IssueIndicatorLayerInfo issueIndicatorLayerInfo = issueIndicatorLayerInfoMapper.selectOne(issueIndicatorLayerInfoLambdaQueryWrapper);
                issueIndicatorInfo.setLayerNumber(issueIndicatorLayerInfo.getNumber());
                issueIndicatorInfoMapper.insert(issueIndicatorInfo);
            }
        })).sheet("Sheet3").doRead();
    }

}
