package com.jdx.k2.management.web.controller.technical;

import com.jdx.k2.management.domain.dto.technical.HardwareSerialNumberDetailDTO;
import com.jdx.k2.management.domain.dto.technical.HardwareSerialNumberPageDTO;
import com.jdx.k2.management.domain.vo.technical.HardwareSerialNumberPageVO;
import com.jdx.k2.management.service.technical.HardwareInnerParameterInfoService;
import com.jdx.rover.common.domain.page.PageDTO;
import com.jdx.rover.common.domain.page.PageVO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 硬件内参信息controller
 */
@RestController
@RequestMapping
public class HardwareInnerParameterInfoController {

    @Resource
    private HardwareInnerParameterInfoService hardwareInnerParameterInfoService;

    /**
     * 分页查询硬件序列列表
     * @param pageVO
     * @param hardwareSerialNumberPageVO
     * @return
     */
    @PostMapping("/k2/management/hardware_inner_params/hardware_serial_number_page_list")
    public PageDTO<HardwareSerialNumberPageDTO> getHardwareSerialNumberPageList(@Valid PageVO pageVO, @Valid @RequestBody HardwareSerialNumberPageVO hardwareSerialNumberPageVO) {
        return hardwareInnerParameterInfoService.getHardwareSerialNumberPageList(pageVO, hardwareSerialNumberPageVO);
    }

    /**
     * 获取硬件序列号详情
     * @param id
     * @return
     */
    @GetMapping("/k2/management/hardware_inner_params/get_hardware_serial_number_detail")
    public HardwareSerialNumberDetailDTO getHardwareSerialNumberDetail (@RequestParam(value = "id") Integer id) {
        return hardwareInnerParameterInfoService.getHardwareSerialNumberDetail(id);
    }
}
