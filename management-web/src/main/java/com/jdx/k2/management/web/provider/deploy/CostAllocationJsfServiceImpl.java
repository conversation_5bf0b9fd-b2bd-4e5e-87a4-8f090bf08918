package com.jdx.k2.management.web.provider.deploy;

import com.jdx.k2.management.domain.dto.deploy.CostAllocationInfoPageDTO;
import com.jdx.k2.management.domain.dto.deploy.CostPageDTO;
import com.jdx.k2.management.domain.service.deploy.CostAllocationJsfService;
import com.jdx.k2.management.domain.vo.deploy.CostAllocationEditVO;
import com.jdx.k2.management.domain.vo.deploy.CostAllocationInfoPageVO;
import com.jdx.k2.management.service.deploy.CostAllocationService;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.jsf.annotation.ServiceInfo;
import com.jdx.rover.jsf.provider.AbstractProvider;
import com.jdx.rover.jsf.response.JsfResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import java.util.Date;

/**
 * 成本划拨模块jsf
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class CostAllocationJsfServiceImpl extends AbstractProvider<CostAllocationJsfService> implements CostAllocationJsfService{

    private final CostAllocationService costAllocationService;

    @Override
    @ServiceInfo(name = "[成本划拨模块]分页获取成本划拨列表", webUrl = "/k2/management/cost_allocation_info/get_page_list", logResponse = false)
    public HttpResult<CostPageDTO<CostAllocationInfoPageDTO>> getPageList(CostAllocationInfoPageVO pageVo) {
        return JsfResponse.response(() -> costAllocationService.getPageList(pageVo));
    }

    @Override
    @ServiceInfo(name = "[成本划拨模块]编辑成本划拨对象", webUrl = "/k2/management/cost_allocation_info/edit")
    public HttpResult<Void> edit(CostAllocationEditVO costAllocationEditVO) {
        return JsfResponse.response(() -> costAllocationService.edit(costAllocationEditVO));
    }

    @Override
    @ServiceInfo(name = "[成本划拨模块]ops创建数据")
    public HttpResult<Void> opsCreateData() {
        return JsfResponse.response(costAllocationService::opsCreateData);
    }

    @Override
    @ServiceInfo(name = "[成本划拨模块]ops站点状态记录")
    public HttpResult<Void> recordStationStatus() {
        return JsfResponse.response(() -> costAllocationService.recordStationStatus(new Date()));
    }

    @Override
    @ServiceInfo(name = "[成本划拨模块]ops记录成本划拨明细")
    public HttpResult<Void> recordCostAllocationDetail() {
        return JsfResponse.response(() -> costAllocationService.recordCostAllocationDetail(new Date()));
    }

    @Override
    @ServiceInfo(name = "[成本划拨模块]ops生成成本划拨")
    public HttpResult<Void> generateCostAllocation(Date date) {
        return JsfResponse.response(() -> costAllocationService.generateCostAllocation(date));
    }
}
