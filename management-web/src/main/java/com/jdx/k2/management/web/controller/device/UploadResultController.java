/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.k2.management.web.controller.device;

import com.jdx.k2.management.common.annotation.OperationLog;
import com.jdx.k2.management.common.enums.LogModuleEnum;
import com.jdx.k2.management.domain.dto.common.UploadResultDTO;
import com.jdx.k2.management.domain.vo.common.UploadResultDownVO;
import com.jdx.k2.management.domain.vo.common.UploadResultVO;
import com.jdx.k2.management.service.device.UploadResultService;
import com.jdx.rover.common.domain.page.PageDTO;
import com.jdx.rover.common.domain.page.PageVO;
import com.jdx.rover.metadata.api.domain.enums.UploadTypeEnum;
import com.jdx.rover.metadata.api.domain.enums.common.OperationTypeEnum;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

/**
 * 上传结果Controller
 *
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Slf4j
@RequestMapping
public class UploadResultController {
    /**
     * 上传结果Service.
     */
    @Resource
    private UploadResultService uploadResultService;

    /**
     * 分页查询上传结果数据列表
     */
    @PostMapping("/k2/management/upload/device_batch_add_log")
    public PageDTO<UploadResultDTO> search(@Valid PageVO pageVO, @Valid @RequestBody UploadResultVO uploadResultVO) {
        return uploadResultService.search(pageVO, uploadResultVO);
    }

    /**
     * 批量上传车辆信息
     */
    @PostMapping("/k2/management/upload/device_batch_add")
    public UploadResultDTO vehicleBatchAdd(@RequestPart(value = "file") MultipartFile file) {
        return uploadResultService.upload(file, UploadTypeEnum.VEHICLE.getValue());
    }

    /**
     * 导入更多设备号
     */
    @PostMapping("/k2/management/upload/device_card_no_batch_add")
    public UploadResultDTO vehicleCardNoBatchAdd(@RequestPart(value = "file") MultipartFile file) {
        return uploadResultService.upload(file, UploadTypeEnum.CARD_NO.getValue());
    }

    /**
     * 下载上传结果
     */
    @OperationLog(module = LogModuleEnum.UPLOAD_RESULT, type = OperationTypeEnum.DOWNLOAD, description = "下载上传结果")
    @GetMapping("/k2/management/upload/upload_result_down")
    public void uploadResultDown(@Valid UploadResultDownVO uploadResultDownVO, HttpServletResponse response) {
        uploadResultService.uploadResultDown(uploadResultDownVO, response);
    }

    /**
     * 获取下载url
     */
    @GetMapping("/k2/management/upload/get_download_url")
    public Map<String, String> getDownloadUrl(@Valid @RequestParam(value = "type") Integer type){
        return uploadResultService.getDownLoadUrl(type);
    }
}
