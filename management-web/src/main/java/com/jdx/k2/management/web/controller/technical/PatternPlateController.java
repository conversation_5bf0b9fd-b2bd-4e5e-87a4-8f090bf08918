/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.k2.management.web.controller.technical;

import com.jdx.k2.management.domain.dto.technical.PatternPlateDTO;
import com.jdx.k2.management.domain.dto.technical.PatternPlateListDTO;
import com.jdx.k2.management.domain.vo.technical.PatternPlateAddVO;
import com.jdx.k2.management.domain.vo.technical.PatternPlateListGetPageListVO;
import com.jdx.k2.management.service.technical.PatternPlateService;
import com.jdx.rover.common.domain.page.PageDTO;
import com.jdx.rover.common.domain.page.PageVO;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 影子系统-模板管理-controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping
public class PatternPlateController {

    @Resource
    private PatternPlateService patternPlateService;

    /**
     * 1、分页查询模板列表
     */
    @PostMapping("/k2/management/pattern_plate/pattern_plate_page")
    public PageDTO<PatternPlateDTO> getPatternPlatePageList(@Valid PageVO pageVO, @Valid @RequestBody PatternPlateListGetPageListVO patternPlateListGetPageListVO) {
        return patternPlateService.getPatternPlatePageList(pageVO, patternPlateListGetPageListVO);
    }

    /**
     * 2、新增模板
     */
    @PostMapping("/k2/management/pattern_plate/pattern_plate_add")
    public String addPatternPlate(@Valid @RequestBody PatternPlateAddVO patternPlateAddVO) {
        return patternPlateService.addPatternPlate(patternPlateAddVO);
    }

    /**
     * 3、根据事件获取模板内容
     */
    @GetMapping("/k2/management/pattern_plate/pattern_plate_get_content/{eventId}")
    public String getPatternPlateContent(@PathVariable("eventId") Integer eventId) {
        return patternPlateService.getPatternPlateContent(eventId);
    }

    /**
     * 4、同步模板到影子系统
     */
    @GetMapping("/k2/management/pattern_plate/pattern_plate_refresh")
    public List<String> refreshPatternPlate(PatternPlateListGetPageListVO patternPlateListGetPageListVO) {
        return patternPlateService.refreshPatternPlate(patternPlateListGetPageListVO);
    }

    /**
     * 5、删除模板
     */
    @GetMapping("/k2/management/pattern_plate/pattern_plate_delete/{eventId}")
    public Integer deletePatternPlate(@PathVariable("eventId") Integer eventId) {
        return patternPlateService.deletePatternPlate(eventId);
    }

    /**
     * 6、模板列表
     */
    @GetMapping("/k2/management/pattern_plate/pattern_plate_list")
    public List<PatternPlateListDTO> getPatternPlateList(PatternPlateListGetPageListVO patternPlateListGetPageListVO) {
        return patternPlateService.getPatternPlateList(patternPlateListGetPageListVO);
    }

}
