package com.jdx.k2.management.web.provider.mobile;

import com.jdx.k2.management.common.enums.base.BaseResponseEnum;
import com.jdx.k2.management.domain.dto.mobile.MobileHardTypeDTO;
import com.jdx.k2.management.domain.service.mobile.MobileRequireInfoJsfService;
import com.jdx.k2.management.domain.vo.mobile.MobileAddRequireVO;
import com.jdx.k2.management.service.mobile.MobileRequireInfoService;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.jsf.annotation.ServiceInfo;
import com.jdx.rover.jsf.provider.AbstractProvider;
import com.jdx.rover.jsf.response.JsfResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import java.util.List;

/**
 * 小程序硬件提报相关接口实现类
 */
@Service
@RequiredArgsConstructor
public class MobileRequireInfoJsfServiceImpl extends AbstractProvider<MobileRequireInfoJsfService> implements MobileRequireInfoJsfService {

    private final MobileRequireInfoService mobileRequireInfoService;

    @Override
    @ServiceInfo(name = "[小程序硬件提报]提报维修单", webUrl = "/k2/management/mobile/require/add_vehicle_require")
    public HttpResult<Void> addVehicleRequire(MobileAddRequireVO mobileAddRequireVO) {
        BaseResponseEnum baseResponseEnum = mobileRequireInfoService.addVehicleRequire(mobileAddRequireVO);
        return new HttpResult<>(baseResponseEnum.getCode(), baseResponseEnum.getMessage(), null);
    }

    @Override
    @ServiceInfo(name = "[小程序硬件提报]获取硬件类型", webUrl = "/k2/management/mobile/require/get_hardware_type", logResponse = false)
    public HttpResult<List<MobileHardTypeDTO>> getHardwareType() {
        return JsfResponse.response(mobileRequireInfoService::getHardwareType);
    }
}