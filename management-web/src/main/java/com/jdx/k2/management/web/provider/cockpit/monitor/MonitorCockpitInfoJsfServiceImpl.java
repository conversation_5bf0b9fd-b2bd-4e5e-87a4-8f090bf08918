package com.jdx.k2.management.web.provider.cockpit.monitor;

import com.jdx.k2.management.domain.dto.cockpit.CockpitListDTO;
import com.jdx.k2.management.domain.dto.cockpit.CockpitTeamPositionDTO;
import com.jdx.k2.management.domain.dto.cockpit.CockpitTeamStationCityDTO;
import com.jdx.k2.management.domain.dto.cockpit.CockpitTeamVehicleInfoDTO;
import com.jdx.k2.management.domain.dto.cockpit.CockpitVehicleInfoDTO;
import com.jdx.k2.management.domain.service.cockpit.monitor.MonitorCockpitInfoJsfService;
import com.jdx.k2.management.domain.vo.cockpit.CockpitListVO;
import com.jdx.k2.management.domain.vo.cockpit.CockpitTeamPositionVO;
import com.jdx.k2.management.domain.vo.cockpit.CockpitTeamStationVO;
import com.jdx.k2.management.domain.vo.cockpit.CockpitTeamVehicleVO;
import com.jdx.k2.management.domain.vo.cockpit.CockpitVehicleInfoGetVO;
import com.jdx.k2.management.service.cockpit.CockpitInfoService;
import com.jdx.k2.management.service.cockpit.CockpitTeamInfoService;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.jsf.annotation.ServiceInfo;
import com.jdx.rover.jsf.provider.AbstractProvider;
import com.jdx.rover.jsf.response.JsfResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import java.util.List;

/**
 * 监控驾驶舱信息 JSF 服务实现类
 * <AUTHOR>
 * @date 2025/2/18
 */
@Service
@RequiredArgsConstructor
public class MonitorCockpitInfoJsfServiceImpl extends AbstractProvider<MonitorCockpitInfoJsfService> implements MonitorCockpitInfoJsfService {

    private final CockpitTeamInfoService cockpitTeamInfoService;

    private final CockpitInfoService cockpitInfoService;

    @Override
    @ServiceInfo(name = "[监控驾舱接口]获取驾驶舱团队下边坐席信息列表", webUrl = "/k2/management/monitor/cockpit_team/get_cockpit_list_info", logResponse = false)
    public HttpResult<List<CockpitListDTO>> getCockpitListInfo(CockpitListVO cockpitListVO) {
        return JsfResponse.response(() -> cockpitTeamInfoService.getCockpitListInfo(cockpitListVO));
    }

    @Override
    @ServiceInfo(name = "[监控驾舱接口]获取驾驶团队车辆信息", webUrl = "/k2/management/monitor/cockpit_team/get_device_info")
    public HttpResult<List<CockpitTeamVehicleInfoDTO>> getDeviceInfo(CockpitTeamVehicleVO cockpitTeamVehicleVO) {
        return JsfResponse.response(() -> cockpitTeamInfoService.getDeviceInfo(cockpitTeamVehicleVO));
    }

    @Override
    @ServiceInfo(name = "[监控驾舱接口]获取驾驶团队城市站点联动信息", webUrl = "/k2/management/monitor/cockpit_team/get_city_station_info")
    public HttpResult<List<CockpitTeamStationCityDTO>> getCityStationInfo(CockpitTeamStationVO cockpitTeamStationVO) {
        return JsfResponse.response(() -> cockpitTeamInfoService.getCityStationInfo(cockpitTeamStationVO));
    }

    @Override
    @ServiceInfo(name = "[监控驾舱接口]获取驾舱团队下边城市/站点/车辆信息", webUrl = "/k2/management/monitor/cockpit_team/get_position_list", logResponse = false)
    public HttpResult<List<CockpitTeamPositionDTO>> getPositionList(CockpitTeamPositionVO cockpitTeamPositionVO) {
        return JsfResponse.response(() -> cockpitTeamInfoService.getPositionList(cockpitTeamPositionVO));
    }

    @Override
    @ServiceInfo(name = "[监控驾舱接口]获取驾驶舱车辆", webUrl = "/k2/management/monitor/cockpit/get_vehicle_list", logResponse = false)
    public HttpResult<List<CockpitVehicleInfoDTO>> getVehicleList(CockpitVehicleInfoGetVO cockpitVehicleInfoGetVO) {
        String cockpitNumber = cockpitVehicleInfoGetVO.getCockpitNumber();
        return JsfResponse.response(() -> cockpitInfoService.getBindVehicleInfo(cockpitNumber));
    }
}