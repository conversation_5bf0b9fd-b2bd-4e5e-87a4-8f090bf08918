server:
  port: 8001
spring:
  datasource:
    url: *****************************************************************************************************************************************************************************************************************************************
    username: root
    password: jdlX2022
  cloud:
    nacos:
      discovery:
        server-addr: http://jdxnacos-beta.jd.local/
        group: ${spring.profiles.active}_group
        username: nacos
        password: nacos-beta@jdlX2022
  redis:
    host: redis-hb3hpfz16cbi-proxy-nlb.jvessel-open-hb.jdcloud.com
    password: jdlX2022
    database: 3
  kafka:
    bootstrap-servers: broker-kafka-poiybufu79-az1-0.jvessel-open-hb.jdcloud.com:9092,broker-kafka-poiybufu79-az1-1.jvessel-open-hb.jdcloud.com:9092,broker-kafka-poiybufu79-az1-2.jvessel-open-hb.jdcloud.com:9092
    client-id: roverVehicle
    producer:
      acks: 1
      retries: 0
      batch-size: 16384
      buffer-memory: 33554432
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
    listener:
      missing-topics-fatal: false
  cache:
    type: caffeine
    caffeine:
      spec: "maximumSize=500,expireAfterWrite=600s"
coding:
  token: WIXMI2T1HC55mcOoM1g6
  isPullMergeRequestOfCoding: false
  pullRepoId: 513291
  targetBranch: master,beta
jagile:
  appId: jdxvehicle
  appSecret: 68c600ea-6c07-49a2-9c59-cd4a59850b35
  jagileUrl: http://api-gateway.jd.com/jacp/api/rest
  cardCodeLength: 14
  optErp: org.jdx.operation1
rover-http:
  post-stop-sync: http://testugvboxvrf.jd.com/ugv_tvt/pushRoverStop.do
vehicle-require:
  vehicleManagementUrl: https://jdxvehicle-ui-beta.jdl.cn/app/vehicleManagement
  requireInfoUrl: https://jdxvehicle-ui-beta.jdl.cn/app/repairOrderManagement
  requireUserGroupName: 维修人员
  requireUserGroupMail: <EMAIL>
jsf:
  consumer:
    consumer-config:
      - interfaceId: "com.jdl.express.transport.api.SecondaryTransportLineRecordApi"
        alias: express-transport-uat
      - interfaceId: "com.jd.x.simulation.task.platform.rpc.SimulationJobService"
        alias: jobRpcService_test
jmq:
  address: nameserver.jmq.jd.local:80
  password: 5400ffbc6fe14391a86706b973609478
  group: basicvehiclewebtest
  sendTimeout: 300
  topic:
    producer:
      metadata_box_message: metadata_box_message_${spring.profiles.active}
      metadata_cockpit_team_message: metadata_cockpit_team_message_${spring.profiles.active}
      metadata_cockpit_vehicle_message: metadata_cockpit_vehicle_message_${spring.profiles.active}
      metadata_device_status_message: metadata_device_status_message_${spring.profiles.active}
      metadata_issue_message: metadata_issue_message_${spring.profiles.active}
      metadata_station_message: metadata_station_message_${spring.profiles.active}
      metadata_stop_message: metadata_stop_message_${spring.profiles.active}
      metadata_user_station_message: metadata_user_station_message_${spring.profiles.active}
      metadata_vehicle_exception_message: metadata_vehicle_exception_message_${spring.profiles.active}
      metadata_vehicle_message: metadata_vehicle_message_${spring.profiles.active}
      metadata_vehicle_require_info: metadata_vehicle_require_info_${spring.profiles.active}
      metadata_vehicle_stop_message: metadata_vehicle_stop_message_${spring.profiles.active}
      metadata_warehouse_config_change_message: metadata_warehouse_config_change_message_${spring.profiles.active}
      operation_user_vehicle_change: operation_user_vehicle_change_${spring.profiles.active}
      metadata_error_code_translate_change_message: metadata_error_code_translate_change_message_${spring.profiles.active}
      metadata_integrate_station_message: metadata_integrate_station_message_${spring.profiles.active}
      metadata_wh_work_mode_message: metadata_wh_work_mode_message_${spring.profiles.active}
      metadata_wh_shelf_message: metadata_wh_shelf_message_${spring.profiles.active}
      metadata_wh_dt_st_message: metadata_wh_dt_st_message_${spring.profiles.active}
      metadata_wh_shelf_type_message: metadata_wh_shelf_type_message_${spring.profiles.active}
      metadata_wh_stop_range_message: metadata_wh_stop_range_message_${spring.profiles.active}
    consumer:
      rover-infra-xingyun-create-bug: rover-infra-xingyun-create-bug-${spring.profiles.active}
      deployment_plan_info: deployment_plan_info_${spring.profiles.active}
deviceRoleGroup:
  vehicle: vehicle-role-group
  robot: JSZ202302271443020001
  warehouse: warehouse-role-group
ducc:
  config:
    name: k2_management_config
    uri: ucc://jdos_basic-vehicle-web-test:<EMAIL>/v1/namespace/vehicle_web_namespace_test/config/k2_management_config/profiles/beta?longPolling=60000&necessary=true
project:
  local:
    cache:
      localCacheEvictTopic: local:cache:evict:basic_vehicle
bug:
  simulation:
    url: http://simulation-staging.jd.com/sim/task/detail/
  manager:
    url: https://jdxvehicle-ui-beta.jdl.cn/app/testing
mr:
  manager:
    url: https://jdxvehicle-ui-beta.jdl.cn/app/mrManage?mrNumber=
neolix:
  host: https://scapi.test.neolix.net