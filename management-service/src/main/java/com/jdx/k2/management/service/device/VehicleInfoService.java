/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.k2.management.service.device;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jdx.k2.management.common.config.DeviceRoleGroupProperties;
import com.jdx.k2.management.common.config.DuccProperties;
import com.jdx.k2.management.common.config.ProductKeyProperties;
import com.jdx.k2.management.common.enums.VehicleConfInfoStatusEnum;
import com.jdx.k2.management.common.enums.base.BaseResponseEnum;
import com.jdx.k2.management.common.exception.AppRuntimeException;
import com.jdx.k2.management.common.utils.PageUtil;
import com.jdx.k2.management.domain.bo.AddressInfo;
import com.jdx.k2.management.domain.bo.DeviceHardwareDetailBO;
import com.jdx.k2.management.domain.bo.device.IntelligentDeviceAddBO;
import com.jdx.k2.management.domain.dto.common.AddressInfoDTO;
import com.jdx.k2.management.domain.dto.device.DeviceAddressBriefInfoDTO;
import com.jdx.k2.management.domain.dto.device.DeviceBaseDetailDTO;
import com.jdx.k2.management.domain.dto.device.DeviceHardwareDTO;
import com.jdx.k2.management.domain.dto.device.DeviceTypeHardwareDTO;
import com.jdx.k2.management.domain.dto.device.HardwareModelInfoDTO;
import com.jdx.k2.management.domain.dto.device.SensorSchemeDetailDTO;
import com.jdx.k2.management.domain.dto.device.VehicleCardNoDTO;
import com.jdx.k2.management.domain.dto.device.VehicleInfoDTO;
import com.jdx.k2.management.domain.dto.device.VehicleInfoToRequireDTO;
import com.jdx.k2.management.domain.po.device.DeviceBaseInfo;
import com.jdx.k2.management.domain.po.device.DeviceHardwareInfo;
import com.jdx.k2.management.domain.po.device.DeviceTypeBaseInfo;
import com.jdx.k2.management.domain.po.device.DeviceTypeHardwareInfo;
import com.jdx.k2.management.domain.po.device.HardwareType;
import com.jdx.k2.management.domain.po.device.Manufactory;
import com.jdx.k2.management.domain.po.device.RobotInfo;
import com.jdx.k2.management.domain.po.device.SensorScheme;
import com.jdx.k2.management.domain.po.device.VehicleInfo;
import com.jdx.k2.management.domain.po.other.CompanyStationInfo;
import com.jdx.k2.management.domain.po.device.Grid;
import com.jdx.k2.management.domain.po.device.HardwareModel;
import com.jdx.k2.management.domain.po.device.Pallet;
import com.jdx.k2.management.domain.po.device.VehicleCardNo;
import com.jdx.k2.management.domain.po.device.Box;
import com.jdx.k2.management.domain.po.device.BoxGridPalletTemplate;
import com.jdx.k2.management.domain.po.device.BoxGridTemplate;
import com.jdx.k2.management.domain.po.device.BoxTemplate;
import com.jdx.k2.management.domain.po.station.StationBaseInfo;
import com.jdx.k2.management.domain.vo.device.ChangeDeviceNameOrApiKeyVO;
import com.jdx.k2.management.domain.vo.device.DeviceAddressBriefInfoVO;
import com.jdx.k2.management.domain.vo.device.DeviceHardwareModelVO;
import com.jdx.k2.management.domain.vo.device.VehicleCardNoVO;
import com.jdx.k2.management.domain.vo.device.VehicleInfoAddVO;
import com.jdx.k2.management.domain.vo.device.VehicleInfoGetPageListVO;
import com.jdx.k2.management.domain.vo.device.VehicleInfoVO;
import com.jdx.k2.management.domain.vo.device.CheckVehicleVO;
import com.jdx.k2.management.manager.business.CityManager;
import com.jdx.k2.management.manager.feign.IntelligentDeviceServerDeviceManager;
import com.jdx.k2.management.manager.feign.OauthServerFeignManager;
import com.jdx.k2.management.manager.jmq.JmqBoxProducer;
import com.jdx.k2.management.manager.jmq.JmqDeviceProducer;
import com.jdx.k2.management.repository.mapper.common.CityMapper;
import com.jdx.k2.management.repository.mapper.device.BoxGridPalletTemplateMapper;
import com.jdx.k2.management.repository.mapper.device.BoxGridTemplateMapper;
import com.jdx.k2.management.repository.mapper.device.BoxMapper;
import com.jdx.k2.management.repository.mapper.device.BoxTemplateMapper;
import com.jdx.k2.management.repository.mapper.device.DeviceBaseInfoMapper;
import com.jdx.k2.management.repository.mapper.device.DeviceHardwareInfoMapper;
import com.jdx.k2.management.repository.mapper.device.DeviceTypeBaseInfoMapper;
import com.jdx.k2.management.repository.mapper.device.DeviceTypeHardwareInfoMapper;
import com.jdx.k2.management.repository.mapper.device.GridMapper;
import com.jdx.k2.management.repository.mapper.device.HardwareModelMapper;
import com.jdx.k2.management.repository.mapper.device.HardwareTypeMapper;
import com.jdx.k2.management.repository.mapper.device.ManufactoryMapper;
import com.jdx.k2.management.repository.mapper.device.PalletMapper;
import com.jdx.k2.management.repository.mapper.device.RobotInfoMapper;
import com.jdx.k2.management.repository.mapper.device.SensorSchemeDetailMapper;
import com.jdx.k2.management.repository.mapper.device.SensorSchemeMapper;
import com.jdx.k2.management.repository.mapper.device.VehicleCardNoMapper;
import com.jdx.k2.management.repository.mapper.device.VehicleInfoMapper;
import com.jdx.k2.management.repository.mapper.other.CompanyStationInfoMapper;
import com.jdx.k2.management.repository.mapper.station.StationBaseInfoMapper;
import com.jdx.rover.common.domain.page.PageDTO;
import com.jdx.rover.common.domain.page.PageVO;
import com.jdx.rover.device.jsfapi.domain.dto.device.DeviceSingleAddDTO;
import com.jdx.rover.jsf.util.JsfLoginUtil;
import com.jdx.rover.metadata.api.domain.enums.DeviceHardwareSourceEnum;
import com.jdx.rover.metadata.api.domain.enums.OperatorEnum;
import com.jdx.rover.metadata.api.domain.enums.ProductTypeEnum;
import com.jdx.rover.metadata.api.domain.enums.SupplierEnum;
import com.jdx.rover.metadata.api.domain.enums.TagEnum;
import com.jdx.rover.metadata.api.domain.enums.VehicleBusinessTypeEnum;
import com.jdx.rover.metadata.api.domain.enums.VehicleCheckStatusEnum;
import com.jdx.rover.metadata.api.domain.enums.VehicleHardwareStatusEnum;
import com.jdx.rover.metadata.api.domain.enums.VehicleOwnerUseCaseEnum;
import com.jdx.rover.metadata.api.domain.enums.VehicleStageEnum;
import com.jdx.rover.metadata.api.domain.enums.VehicleUseCaseEnum;
import com.jdx.rover.metadata.api.domain.enums.WarehouseWorkModeEnum;
import com.jdx.rover.metadata.api.domain.enums.common.EnableEnum;
import com.jdx.rover.metadata.api.domain.enums.common.YesOrNoEnum;
import com.jdx.rover.metadata.api.domain.enums.kafka.OperationTypeEnum;
import com.jdx.rover.oauth.api.domain.vo.Oauth2RegisteredClientAddVO;
import com.jdx.rover.permission.domain.vo.basic.ClientBasicImportVO;
import com.jdx.rover.permission.jsf.service.basic.PermissionClientInfoBasicService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.CollectionUtils;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import static com.jdx.k2.management.common.constants.Constants.DELETED;
import static com.jdx.k2.management.common.constants.Constants.K2_MANAGEMENT_PLATFORM;

/**
 * 车辆信息管理Service
 *
 * <AUTHOR> wangguotai
 * @version 1.0
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class VehicleInfoService {

    private final VehicleCardNoService vehicleCardNoService;

    private final VehicleCardNoMapper vehicleCardNoMapper;

    private final OauthServerFeignManager oauthServerFeignManager;

    private final BoxMapper boxMapper;

    private final DeviceTypeBaseInfoMapper deviceTypeBaseInfoMapper;

    private final GridMapper gridMapper;

    private final BoxTemplateMapper boxTemplateMapper;

    private final BoxGridTemplateMapper boxGridTemplateMapper;

    private final DeviceBaseInfoMapper deviceBaseInfoMapper;

    private final BoxGridPalletTemplateMapper boxGridPalletTemplateMapper;

    private final PalletMapper palletMapper;

    private final HardwareModelMapper hardwareModelMapper;

    private final HardwareTypeMapper hardwareTypeMapper;

    private final CityMapper cityMapper;

    private final StationBaseInfoMapper stationBaseInfoMapper;

    private final CompanyStationInfoMapper companyStationInfoMapper;
    
    private final DeviceHardwareInfoMapper deviceHardwareInfoMapper;

    private final SensorSchemeMapper sensorSchemeMapper;
    
    private final ManufactoryMapper manufactoryMapper;

    private final VehicleInfoMapper vehicleInfoMapper;
    
    private final RobotInfoMapper robotInfoMapper;
    
    private final DeviceRoleGroupProperties deviceRoleGroupProperties;
    
    private final PermissionClientInfoBasicService permissionClientInfoBasicService;

    private final JmqDeviceProducer jmqDeviceProducer;

    private final IntelligentDeviceServerDeviceManager intelligentDeviceServerDeviceManager;

    private final JmqBoxProducer jmqBoxProducer;

    private final ProductKeyProperties productKeyProperties;

    private final DuccProperties duccProperties;

    private final CityManager cityManager;

    private final DeviceTypeHardwareInfoMapper deviceTypeHardwareInfoMapper;

    private final SensorSchemeDetailMapper sensorSchemeDetailMapper;
    
    

    /**
     * 1、分页查询车辆信息管理数据列表
     */
    public PageDTO<VehicleInfoDTO> search(PageVO pageVO, VehicleInfoGetPageListVO vehicleInfoGetPageListVO) {
        IPage<DeviceBaseInfo> iPage = new Page<>(pageVO.getPageNum(), pageVO.getPageSize());
        QueryWrapper<DeviceBaseInfo> deviceQueryWrapper = new QueryWrapper<>();
        deviceQueryWrapper.like(StringUtils.isNotBlank(vehicleInfoGetPageListVO.getSerialNo()), "dbi.serial_no", vehicleInfoGetPageListVO.getSerialNo());
        deviceQueryWrapper.like(StringUtils.isNotBlank(vehicleInfoGetPageListVO.getName()), "dbi.name", vehicleInfoGetPageListVO.getName());
        deviceQueryWrapper.eq(Objects.nonNull(vehicleInfoGetPageListVO.getDeviceTypeBaseId()), "dbi.device_type_base_id", vehicleInfoGetPageListVO.getDeviceTypeBaseId());
        deviceQueryWrapper.eq(StringUtils.isNotBlank(vehicleInfoGetPageListVO.getProductType()), "dbi.product_type", vehicleInfoGetPageListVO.getProductType());
        deviceQueryWrapper.eq(StringUtils.isNotBlank(vehicleInfoGetPageListVO.getBusinessType()), "dbi.business_type", vehicleInfoGetPageListVO.getBusinessType());
        deviceQueryWrapper.eq(Objects.nonNull(vehicleInfoGetPageListVO.getHardwareModelId()), "dhi.hardware_model_id", vehicleInfoGetPageListVO.getHardwareModelId());
        deviceQueryWrapper.like(StringUtils.isNotBlank(vehicleInfoGetPageListVO.getHardwareNumber()), "dhi.hardware_number", vehicleInfoGetPageListVO.getHardwareNumber());
        deviceQueryWrapper.like(StringUtils.isNotBlank(vehicleInfoGetPageListVO.getAndroidDeviceId()), "dbi.android_device_id", vehicleInfoGetPageListVO.getAndroidDeviceId());
        deviceQueryWrapper.eq(StringUtils.isNotBlank(vehicleInfoGetPageListVO.getSupplier()), "dbi.supplier", vehicleInfoGetPageListVO.getSupplier());
        if (!CollectionUtils.isEmpty(vehicleInfoGetPageListVO.getTagList())) {
            int tag = 0;
            for (int i : vehicleInfoGetPageListVO.getTagList()) {
                tag += 1 << i;
            }
            deviceQueryWrapper.gt("dbi.tag & " + tag, 0);
        }
        deviceQueryWrapper.eq("dbi.deleted", DELETED);
        deviceQueryWrapper.eq("dhi.deleted", DELETED);
        deviceQueryWrapper.orderByDesc("dbi.name");
        deviceQueryWrapper.groupBy("dbi.id");
        IPage<DeviceBaseInfo> vehicleIPage = deviceBaseInfoMapper.selectPageByParams(iPage, deviceQueryWrapper);
        List<VehicleInfoDTO> vehicleInfoDTOList = vehicleIPage.getRecords().stream().map(this::convertPoToDto).collect(Collectors.toList());
        return PageUtil.toPageDTO(vehicleIPage, vehicleInfoDTOList);
    }

    /**
     * 2、新增车辆信息管理
     */
    @Transactional(rollbackFor = Exception.class)
    public BaseResponseEnum add(VehicleInfoAddVO vehicleInfoAddVO) {
        String username = JsfLoginUtil.getUsername();

        //无人车三方车默认值处理
        if (ProductTypeEnum.VEHICLE.getValue().equals(vehicleInfoAddVO.getProductType())) {
            //供应商必填
            if (StringUtils.isBlank(vehicleInfoAddVO.getSupplier())) {
                throw new AppRuntimeException(BaseResponseEnum.ERROR_DEVICE_VEHICLE_SUPPLIER_NOT_NULL);
            }
            //非京东供应商将车牌号置为车架号,给默认的货箱模板
            if (!SupplierEnum.JD.getValue().equals(vehicleInfoAddVO.getSupplier())) {
                //车架号
                vehicleInfoAddVO.setSerialNo(vehicleInfoAddVO.getName());
                //货箱模板
                Map<String, Map<String, Object>> thirdVehicleDefaultValueOfSupplier = duccProperties.getThirdVehicleDefaultValueOfSupplier();
                if (!CollectionUtils.isEmpty(thirdVehicleDefaultValueOfSupplier)) {
                    Map<String, Object> map = thirdVehicleDefaultValueOfSupplier.get(vehicleInfoAddVO.getSupplier());
                    if (!CollectionUtils.isEmpty(map)) {
                        vehicleInfoAddVO.setBoxTemplateId((Integer) map.get("boxTemplateId"));
                    }
                }
            }
        }

        if(checkHardwareModel(vehicleInfoAddVO)){
            return BaseResponseEnum.ERROR_EMPTY_HARDWARE_MODEL;
        }
        if (checkExistVehicleSerialNo(vehicleInfoAddVO.getSerialNo())) {
            return BaseResponseEnum.ERROR_VEHICLE_ADD_SERIAL_NO;
        }
        if (checkExistDeviceName(vehicleInfoAddVO.getName())) {
            return BaseResponseEnum.ERROR_VEHICLE_ADD_LICENSE;
        }
        if (vehicleCardNoService.checkExistVehicleCardNoList(vehicleInfoAddVO.getCardNoList())) {
            return BaseResponseEnum.ERROR_VEHICLE_CARD_NO_DUPLICATE;
        }
        //保存设备信息
        DeviceBaseInfo deviceBaseInfo = new DeviceBaseInfo();
        deviceBaseInfo.setName(vehicleInfoAddVO.getName());
        deviceBaseInfo.setSerialNo(vehicleInfoAddVO.getSerialNo());
        deviceBaseInfo.setProductType(vehicleInfoAddVO.getProductType());
        deviceBaseInfo.setBusinessType(vehicleInfoAddVO.getBusinessType());
        deviceBaseInfo.setStationBaseId(vehicleInfoAddVO.getStationBaseId());
        deviceBaseInfo.setBoxTemplateId(vehicleInfoAddVO.getBoxTemplateId());
        deviceBaseInfo.setDeviceTypeBaseId(vehicleInfoAddVO.getDeviceTypeBaseId());
        deviceBaseInfo.setIsVirtual(Objects.isNull(vehicleInfoAddVO.getIsVirtual()) ? 0 : vehicleInfoAddVO.getIsVirtual());
        deviceBaseInfo.setVCode(vehicleInfoAddVO.getName() + "-1");
        //表中必填项
        //如果是三方车,直接到交付阶段
        if (Objects.nonNull(vehicleInfoAddVO.getSupplier()) && !SupplierEnum.JD.getValue().equals(vehicleInfoAddVO.getSupplier())) {
            deviceBaseInfo.setVehicleStage(VehicleStageEnum.DELIVERY.getValue());
            deviceBaseInfo.setHardwareStatus(VehicleHardwareStatusEnum.USE.getValue());
            deviceBaseInfo.setOwnerUseCase(VehicleOwnerUseCaseEnum.OPEN.getValue());
        } else {
            deviceBaseInfo.setVehicleStage(VehicleStageEnum.PRODUCT.getValue());
            deviceBaseInfo.setHardwareStatus(VehicleHardwareStatusEnum.NEW.getValue());
            deviceBaseInfo.setOwnerUseCase(VehicleOwnerUseCaseEnum.PRODUCT.getValue());
        }

        Box box = saveBoxAndGrid(vehicleInfoAddVO.getBoxTemplateId(), deviceBaseInfo);
        deviceBaseInfo.setBoxId(box.getId());
        //供应商
        deviceBaseInfo.setSupplier(vehicleInfoAddVO.getSupplier());

        //设置车辆标识
        int tag = 0;
        if (!CollectionUtils.isEmpty(vehicleInfoAddVO.getTagList())) {
            for (int i : vehicleInfoAddVO.getTagList()) {
                tag += 1 << i;
            }
        }
        deviceBaseInfo.setTag(tag);

        //判断车型是否配置
        DeviceTypeBaseInfo vehicleType = deviceTypeBaseInfoMapper.selectById(vehicleInfoAddVO.getDeviceTypeBaseId());
        if (Objects.nonNull(vehicleType)) {
            deviceBaseInfo.setVehicleConfIssueStatus(YesOrNoEnum.YES.getValue().equals(vehicleType.getIsConfigure()) ? VehicleConfInfoStatusEnum.VEHICLE_TYPE_CONFIGURE.getValue() : (VehicleConfInfoStatusEnum.VEHICLE_TYPE_NOT_CONFIGURE.getValue()));
        } else {
            deviceBaseInfo.setVehicleConfIssueStatus(VehicleConfInfoStatusEnum.VEHICLE_TYPE_NOT_CONFIGURE.getValue());
        }

        //通用设备平台注册车辆
        IntelligentDeviceAddBO intelligentDeviceAddBO = new IntelligentDeviceAddBO();
        //产品型号
        if (Objects.nonNull(vehicleType)) {
            //通用设备平台产品型号
            intelligentDeviceAddBO.setProductModelNo(String.valueOf(vehicleType.getId()));
        }
        //分组信息
        if (Objects.nonNull(deviceBaseInfo.getStationBaseId())) {
            StationBaseInfo stationBaseInfo = stationBaseInfoMapper.selectById(deviceBaseInfo.getStationBaseId());
            if (Objects.nonNull(stationBaseInfo)) {
                intelligentDeviceAddBO.setGroupNo(stationBaseInfo.getGroupNo());
            }
        }
        //设备名称+产品标识
        if (ProductTypeEnum.VEHICLE.getValue().equals(deviceBaseInfo.getProductType())) {
            //如果是无人车使用车牌号注册
            intelligentDeviceAddBO.setDeviceName(deviceBaseInfo.getName());
            //无人车产品标识
            intelligentDeviceAddBO.setProductKey(productKeyProperties.getVehicle());
        } else if (ProductTypeEnum.ROBOT.getValue().equals(deviceBaseInfo.getProductType())) {
            //如果是小红车使用车架号注册
            intelligentDeviceAddBO.setDeviceName(deviceBaseInfo.getSerialNo());
            //小红车机器人产品标识
            intelligentDeviceAddBO.setProductKey(productKeyProperties.getRobot());
        } else {
            //如果是多合一使用车架号注册
            intelligentDeviceAddBO.setDeviceName(deviceBaseInfo.getSerialNo());
            //多合一将车牌号置为备注名称
            intelligentDeviceAddBO.setRemarkName(deviceBaseInfo.getName());
            //多合一机器人产品标识
            intelligentDeviceAddBO.setProductKey(productKeyProperties.getIntegrate());
        }

        //设备标签暂时不传,看后续需求

        //添加设备
        DeviceSingleAddDTO deviceSingleAddDTO = intelligentDeviceServerDeviceManager.addIntelligentDevice(intelligentDeviceAddBO);

        String apiKey = deviceSingleAddDTO.getApiKey();
        deviceBaseInfo.setApiKey(apiKey);
        deviceBaseInfo.setCreateUser(username);
        deviceBaseInfo.setModifyUser(username);
        deviceBaseInfoMapper.insert(deviceBaseInfo);

        if (ProductTypeEnum.VEHICLE.getValue().equals(vehicleInfoAddVO.getProductType())) {
            //插入vehicle_info表
            VehicleInfo vehicleInfo = new VehicleInfo();
            vehicleInfo.setDeviceBaseId(deviceBaseInfo.getId());
            if (SupplierEnum.JD.getValue().equals(vehicleInfoAddVO.getSupplier())) {
                vehicleInfo.setUseCaseId(VehicleUseCaseEnum.TEST.getValue());
            } else {
                vehicleInfo.setUseCaseId(VehicleUseCaseEnum.OPERATION.getValue());
            }
            vehicleInfo.setCreateUser(username);
            vehicleInfo.setModifyUser(username);
            vehicleInfoMapper.insert(vehicleInfo);
        } else if (ProductTypeEnum.ROBOT.getValue().equals(vehicleInfoAddVO.getProductType())
                || ProductTypeEnum.WAREHOURSE.getValue().equals(vehicleInfoAddVO.getProductType())) {
            //TODO 插入robot_info表,音量、速度值没有添加入口
            RobotInfo robotInfo = new RobotInfo();
            robotInfo.setWorkMode(WarehouseWorkModeEnum.SYS.getName());
            robotInfo.setDeviceBaseId(deviceBaseInfo.getId());
            robotInfo.setCreateUser(username);
            robotInfo.setModifyUser(username);
            robotInfoMapper.insert(robotInfo);
        }

        //添加箱体硬件到硬件列表
        BoxTemplate boxTemplate = boxTemplateMapper.selectById(vehicleInfoAddVO.getBoxTemplateId());
        if (Objects.nonNull(boxTemplate)) {
            DeviceHardwareModelVO deviceHardwareModelVO = new DeviceHardwareModelVO();
            deviceHardwareModelVO.setHardwareModelId(boxTemplate.getHardwareModelId());
            deviceHardwareModelVO.setNum(1);
            deviceHardwareModelVO.setSource(DeviceHardwareSourceEnum.DEVICE.getValue());
            vehicleInfoAddVO.getDeviceHardwareModelList().add(deviceHardwareModelVO);
        }
        //添加关联的硬件型号
        addDeviceHardwareInfo(deviceBaseInfo.getId(), vehicleInfoAddVO.getDeviceHardwareModelList());

        //保存卡号
        if (!CollectionUtils.isEmpty(vehicleInfoAddVO.getCardNoList())) {
            for (VehicleCardNoVO vehicleCardNoVO : vehicleInfoAddVO.getCardNoList()) {
                vehicleCardNoVO.setVehicleId(deviceBaseInfo.getId());
                vehicleCardNoService.add(vehicleCardNoVO);
            }
        }

//        提交完成后发送消息
        if (TransactionSynchronizationManager.isSynchronizationActive()) {
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    List<String> vehicleNameList = new ArrayList<>();
                    vehicleNameList.add(vehicleInfoAddVO.getName());
                    jmqDeviceProducer.sendUserVehicleChangeMessage(vehicleNameList,null, deviceBaseInfo.getStationBaseId());
                    //V2
                    jmqDeviceProducer.sendMetadataVehicleMessage(OperationTypeEnum.ADD, deviceBaseInfo.getName());
                }
            });
        }
        return BaseResponseEnum.SUCCESS;
    }

    /**
     * 3、获取车辆信息管理详情
     */
    public DeviceBaseDetailDTO getById(Integer id) {
        DeviceBaseInfo deviceBaseInfo = deviceBaseInfoMapper.selectById(id);
        if (Objects.isNull(deviceBaseInfo)) {
            throw new AppRuntimeException(BaseResponseEnum.ERROR_GET_VEHICLE);
        }
        VehicleInfoDTO vehicleInfoDTO = convertPoToDto(deviceBaseInfo);
        DeviceBaseDetailDTO baseDetailDTO = new DeviceBaseDetailDTO();
        baseDetailDTO.setId(vehicleInfoDTO.getId());
        baseDetailDTO.setName(vehicleInfoDTO.getName());
        baseDetailDTO.setSerialNo(vehicleInfoDTO.getSerialNo());
        baseDetailDTO.setProductType(vehicleInfoDTO.getProductType());
        baseDetailDTO.setProductTypeName(vehicleInfoDTO.getProductTypeName());
        baseDetailDTO.setSupplier(vehicleInfoDTO.getSupplier());
        baseDetailDTO.setSupplierName(vehicleInfoDTO.getSupplierName());
        baseDetailDTO.setBusinessType(vehicleInfoDTO.getBusinessType());
        baseDetailDTO.setBusinessTypeName(vehicleInfoDTO.getBusinessTypeName());
        baseDetailDTO.setTagList(vehicleInfoDTO.getTagList());
        baseDetailDTO.setTagName(vehicleInfoDTO.getTagName());
        baseDetailDTO.setBoxTemplateId(vehicleInfoDTO.getBoxTemplateId());
        baseDetailDTO.setBoxTemplateName(vehicleInfoDTO.getBoxTemplateName());
        baseDetailDTO.setHardwareModelName(vehicleInfoDTO.getHardwareModelName());
        baseDetailDTO.setHardwareModelModel(vehicleInfoDTO.getHardwareModelModel());
        baseDetailDTO.setGridNum(vehicleInfoDTO.getGridNum());
        baseDetailDTO.setDeviceTypeBaseId(vehicleInfoDTO.getDeviceTypeBaseId());
        baseDetailDTO.setDeviceTypeName(vehicleInfoDTO.getDeviceTypeName());
        baseDetailDTO.setHardwareStatusName(vehicleInfoDTO.getHardwareStatusName());
        baseDetailDTO.setCheckStatusName(vehicleInfoDTO.getCheckStatusName());
        baseDetailDTO.setAndroidDeviceId(vehicleInfoDTO.getAndroidDeviceId());
        baseDetailDTO.setModifyUser(vehicleInfoDTO.getModifyUser());
        baseDetailDTO.setModifyTime(vehicleInfoDTO.getModifyTime());
        baseDetailDTO.setCardNoList(getCardNoListById(baseDetailDTO.getId()));
        baseDetailDTO.setIsVirtual(deviceBaseInfo.getIsVirtual());
        baseDetailDTO.setIsVirtualName(YesOrNoEnum.getNameByValue(deviceBaseInfo.getIsVirtual()));

        //获取传感器信息
        QueryWrapper<SensorScheme> sensorSchemeQueryWrapper = new QueryWrapper<>();
        sensorSchemeQueryWrapper.eq("dtbi.id", vehicleInfoDTO.getDeviceTypeBaseId());
        sensorSchemeQueryWrapper.eq("s.deleted", DELETED);
        SensorScheme sensorScheme = sensorSchemeMapper.selectByDeviceTypeId(sensorSchemeQueryWrapper);
        if (Objects.nonNull(sensorScheme)) {
            baseDetailDTO.setSensorSchemeId(sensorScheme.getId());
            baseDetailDTO.setSensorSchemeName(sensorScheme.getName());
        }

        //获取厂商信息
        QueryWrapper<Manufactory> manufactoryQueryWrapper = new QueryWrapper<>();
        manufactoryQueryWrapper.eq("dtbi.id", vehicleInfoDTO.getDeviceTypeBaseId());
        manufactoryQueryWrapper.eq("m.deleted", DELETED);
        Manufactory manufactory = manufactoryMapper.selectByDeviceTypeId(manufactoryQueryWrapper);
        if (Objects.nonNull(manufactory)) {
            baseDetailDTO.setManufactoryId(manufactory.getId());
            baseDetailDTO.setManufactoryName(manufactory.getFactoryName());
        }
        List<DeviceHardwareDTO> hardwareDTOList = new ArrayList<>();
        List<DeviceTypeHardwareDTO> deviceTypeHardwareDTOList = new ArrayList<>();
        List<SensorSchemeDetailDTO> sensorSchemeDetailList = new ArrayList<>();
        LambdaQueryWrapper<DeviceHardwareInfo> deviceQueryWrapper = new LambdaQueryWrapper<>();
        deviceQueryWrapper.eq(Objects.nonNull(deviceBaseInfo.getId()), DeviceHardwareInfo::getDeviceBaseId, deviceBaseInfo.getId());
        List<DeviceHardwareInfo> deviceHardwareInfoList = deviceHardwareInfoMapper.selectList(deviceQueryWrapper);
        if (!CollectionUtils.isEmpty(deviceHardwareInfoList)) {
            Set<Integer> hardwareModelIdSet = deviceHardwareInfoList.stream().map(DeviceHardwareInfo::getHardwareModelId).collect(Collectors.toSet());
            QueryWrapper<HardwareType> hardwareModelQueryWrapper = new QueryWrapper<>();
            hardwareModelQueryWrapper.in("hm.id", hardwareModelIdSet);
            hardwareModelQueryWrapper.eq("hm.deleted", DELETED);
            hardwareModelQueryWrapper.groupBy("hm.id");
            List<DeviceHardwareDetailBO> deviceHardwareDetailBOList = hardwareTypeMapper.selectByCondition(hardwareModelQueryWrapper);
            if (!CollectionUtils.isEmpty(deviceHardwareDetailBOList)) {
                Map<Integer, List<DeviceHardwareDetailBO>> deviceHardwareDetailBOMap = deviceHardwareDetailBOList.stream().collect(Collectors.groupingBy(DeviceHardwareDetailBO::getHardwareModelId));
                deviceHardwareInfoList.forEach(deviceHardwareInfo -> {
                    if (deviceHardwareInfo.getSource().equals(DeviceHardwareSourceEnum.DEVICE.getValue())) {
                        if (!CollectionUtils.isEmpty(deviceHardwareDetailBOMap)) {
                            List<DeviceHardwareDetailBO> deviceHardwareDetailBOList1 = deviceHardwareDetailBOMap.get(deviceHardwareInfo.getHardwareModelId());
                            if (!CollectionUtils.isEmpty(deviceHardwareDetailBOList1)) {
                                deviceHardwareDetailBOList1.forEach(deviceHardwareDetailBO -> {
                                    DeviceHardwareDTO deviceHardwareDTO = new DeviceHardwareDTO();
                                    deviceHardwareDTO.setHardwareModelId(deviceHardwareInfo.getHardwareModelId());
                                    deviceHardwareDTO.setHardwareNumber(deviceHardwareInfo.getHardwareNumber());
                                    deviceHardwareDTO.setUserNumber(deviceHardwareInfo.getNum());
                                    deviceHardwareDTO.setHardwareTypeId(deviceHardwareDetailBO.getHardwareTypeId());
                                    deviceHardwareDTO.setHardwareTypeName(deviceHardwareDetailBO.getHardwareTypeName());
                                    deviceHardwareDTO.setHardwareModelModel(deviceHardwareDetailBO.getHardwareModelModel());
                                    deviceHardwareDTO.setHardwareModelName(deviceHardwareDetailBO.getHardwareModelName());
                                    hardwareDTOList.add(deviceHardwareDTO);
                                });
                            }
                        } else {
                            DeviceHardwareDTO deviceHardwareDTO = new DeviceHardwareDTO();
                            deviceHardwareDTO.setHardwareModelId(deviceHardwareInfo.getHardwareModelId());
                            deviceHardwareDTO.setHardwareNumber(deviceHardwareInfo.getHardwareNumber());
                            deviceHardwareDTO.setUserNumber(deviceHardwareInfo.getNum());
                            hardwareDTOList.add(deviceHardwareDTO);
                        }
                    } else if (deviceHardwareInfo.getSource().equals(DeviceHardwareSourceEnum.DEVICE_TYPE.getValue())) {
                        if (!CollectionUtils.isEmpty(deviceHardwareDetailBOMap)) {
                            List<DeviceHardwareDetailBO> deviceHardwareDetailBOList2 = deviceHardwareDetailBOMap.get(deviceHardwareInfo.getHardwareModelId());
                            if (!CollectionUtils.isEmpty(deviceHardwareDetailBOList2)) {
                                deviceHardwareDetailBOList2.forEach(deviceHardwareDetailBO -> {
                                    DeviceTypeHardwareDTO deviceTypeHardwareDTO = new DeviceTypeHardwareDTO();
                                    deviceTypeHardwareDTO.setHardwareNumber(deviceHardwareInfo.getHardwareNumber());
                                    deviceTypeHardwareDTO.setHardwareModelId(deviceHardwareInfo.getHardwareModelId());
                                    deviceTypeHardwareDTO.setUseNumber(deviceHardwareInfo.getNum());
                                    deviceTypeHardwareDTO.setHardwareTypeId(deviceHardwareDetailBO.getHardwareTypeId());
                                    deviceTypeHardwareDTO.setHardwareTypeName(deviceHardwareDetailBO.getHardwareTypeName());
                                    deviceTypeHardwareDTO.setHardwareModelModel(deviceHardwareDetailBO.getHardwareModelModel());
                                    deviceTypeHardwareDTO.setHardwareModelName(deviceHardwareDetailBO.getHardwareModelName());
                                    deviceTypeHardwareDTOList.add(deviceTypeHardwareDTO);
                                });
                            }
                        } else {
                            DeviceTypeHardwareDTO deviceTypeHardwareDTO = new DeviceTypeHardwareDTO();
                            deviceTypeHardwareDTO.setHardwareNumber(deviceHardwareInfo.getHardwareNumber());
                            deviceTypeHardwareDTO.setHardwareModelId(deviceHardwareInfo.getHardwareModelId());
                            deviceTypeHardwareDTO.setUseNumber(deviceHardwareInfo.getNum());
                            deviceTypeHardwareDTOList.add(deviceTypeHardwareDTO);
                        }
                    } else if (deviceHardwareInfo.getSource().equals(DeviceHardwareSourceEnum.SENSOR_SCHEME.getValue())) {
                        if (!CollectionUtils.isEmpty(deviceHardwareDetailBOMap)) {
                            List<DeviceHardwareDetailBO> deviceHardwareDetailBOList3 = deviceHardwareDetailBOMap.get(deviceHardwareInfo.getHardwareModelId());
                            if (!CollectionUtils.isEmpty(deviceHardwareDetailBOList3)) {
                                deviceHardwareDetailBOList3.forEach(deviceHardwareDetailBO -> {
                                    SensorSchemeDetailDTO sensorSchemeDetailDTO = new SensorSchemeDetailDTO();
                                    sensorSchemeDetailDTO.setHardwareModelId(deviceHardwareInfo.getHardwareModelId());
                                    sensorSchemeDetailDTO.setUseNumber(deviceHardwareInfo.getNum());
                                    sensorSchemeDetailDTO.setHardwareNumber(deviceHardwareInfo.getHardwareNumber());
                                    sensorSchemeDetailDTO.setHardwareTypeId(deviceHardwareDetailBO.getHardwareTypeId());
                                    sensorSchemeDetailDTO.setHardwareTypeName(deviceHardwareDetailBO.getHardwareTypeName());
                                    sensorSchemeDetailDTO.setHardwareModelModel(deviceHardwareDetailBO.getHardwareModelModel());
                                    sensorSchemeDetailDTO.setHardwareModelName(deviceHardwareDetailBO.getHardwareModelName());
                                    sensorSchemeDetailDTO.setHardwareTypeUsageName(deviceHardwareDetailBO.getHardwareTypeUsageName());
                                    sensorSchemeDetailList.add(sensorSchemeDetailDTO);
                                });
                            }
                        } else {
                            SensorSchemeDetailDTO sensorSchemeDetailDTO = new SensorSchemeDetailDTO();
                            sensorSchemeDetailDTO.setHardwareModelId(deviceHardwareInfo.getHardwareModelId());
                            sensorSchemeDetailDTO.setUseNumber(deviceHardwareInfo.getNum());
                            sensorSchemeDetailDTO.setHardwareNumber(deviceHardwareInfo.getHardwareNumber());
                            sensorSchemeDetailList.add(sensorSchemeDetailDTO);
                        }
                    }
                });
            }
            baseDetailDTO.setSensorSchemeDetailList(sensorSchemeDetailList);
            baseDetailDTO.setDeviceHardwareList(hardwareDTOList);
            baseDetailDTO.setDeviceTypeHardwareInfoList(deviceTypeHardwareDTOList);
        }
        return baseDetailDTO;
    }

    /**
     * 4、编辑车辆信息管理
     */
    @Transactional(rollbackFor = Exception.class)
    public BaseResponseEnum update(VehicleInfoVO vehicleInfoVO) {
        String username = JsfLoginUtil.getUsername();
        if (checkUpdateExistVehicleSerialNo(vehicleInfoVO.getId(), vehicleInfoVO.getSerialNo())) {
            return BaseResponseEnum.ERROR_VEHICLE_ADD_SERIAL_NO;
        }
        if (checkUpdateExistVehicleLicense(vehicleInfoVO.getId(), vehicleInfoVO.getName())) {
            return BaseResponseEnum.ERROR_VEHICLE_ADD_LICENSE;
        }
        if (vehicleCardNoService.checkUpdateExistVehicleCardNoList(vehicleInfoVO.getCardNoList())) {
            return BaseResponseEnum.ERROR_VEHICLE_CARD_NO_DUPLICATE;
        }
        //获取变更前的车辆信息
        DeviceBaseInfo oldDeviceBaseInfo = deviceBaseInfoMapper.selectById(vehicleInfoVO.getId());

        DeviceBaseInfo newDeviceBaseInfo = new DeviceBaseInfo();
        newDeviceBaseInfo.setId(vehicleInfoVO.getId());
        newDeviceBaseInfo.setName(vehicleInfoVO.getName());
        newDeviceBaseInfo.setSerialNo(vehicleInfoVO.getSerialNo());
        newDeviceBaseInfo.setProductType(vehicleInfoVO.getProductType());
        newDeviceBaseInfo.setBusinessType(vehicleInfoVO.getBusinessType());
        newDeviceBaseInfo.setBoxTemplateId(vehicleInfoVO.getBoxTemplateId());
        newDeviceBaseInfo.setIsVirtual(vehicleInfoVO.getIsVirtual());
        newDeviceBaseInfo.setModifyUser(username);
        //设置系统标签
        int tag = 0;
        if (!CollectionUtils.isEmpty(vehicleInfoVO.getTagList())) {
            for (int i : vehicleInfoVO.getTagList()) {
                tag += 1 << i;
            }
        }
        newDeviceBaseInfo.setTag(tag);
        //判断车型是否更改
        if (!Objects.equals(oldDeviceBaseInfo.getDeviceTypeBaseId(), vehicleInfoVO.getDeviceTypeBaseId())) {
            //车型改变了
            newDeviceBaseInfo.setDeviceTypeBaseId(vehicleInfoVO.getDeviceTypeBaseId());
            DeviceTypeBaseInfo deviceTypeBaseInfo = deviceTypeBaseInfoMapper.selectById(vehicleInfoVO.getDeviceTypeBaseId());
            if (YesOrNoEnum.NO.getValue().equals(deviceTypeBaseInfo.getIsConfigure())) {
                newDeviceBaseInfo.setVehicleConfIssueStatus(VehicleConfInfoStatusEnum.VEHICLE_TYPE_NOT_CONFIGURE.getValue());
            } else {
                if (oldDeviceBaseInfo.getVehicleConfIssueStatus().equals(VehicleConfInfoStatusEnum.VEHICLE_TYPE_CONFIGURE.getValue()) || oldDeviceBaseInfo.getVehicleConfIssueStatus().equals(VehicleConfInfoStatusEnum.VEHICLE_TYPE_NOT_CONFIGURE.getValue())) {
                    newDeviceBaseInfo.setVehicleConfIssueStatus(VehicleConfInfoStatusEnum.VEHICLE_TYPE_CONFIGURE.getValue());
                } else {
                    newDeviceBaseInfo.setVehicleConfIssueStatus(VehicleConfInfoStatusEnum.NOT_ISSUE.getValue());
                }
            }
        }
        //判断箱体模板是否变更
        if (!Objects.equals(vehicleInfoVO.getBoxTemplateId(), oldDeviceBaseInfo.getBoxTemplateId())) {
            Box box = saveBoxAndGrid(vehicleInfoVO.getBoxTemplateId(), newDeviceBaseInfo);
            newDeviceBaseInfo.setBoxId(box.getId());
        }

        int success = deviceBaseInfoMapper.updateById(newDeviceBaseInfo);
        if (success <= 0) {
            return BaseResponseEnum.ERROR_VEHICLE_EDIT;
        }

        //更新关联的设备硬件类型
        //先删除
        LambdaQueryWrapper<DeviceHardwareInfo> deleteQueryWrapper = new LambdaQueryWrapper<>();
        deleteQueryWrapper.eq(DeviceHardwareInfo::getDeviceBaseId, newDeviceBaseInfo.getId());
        deviceHardwareInfoMapper.delete(deleteQueryWrapper);
        List<DeviceHardwareModelVO> deviceHardwareModelVOList = new ArrayList<>();
        deviceHardwareModelVOList.addAll(vehicleInfoVO.getDeviceHardwareModelList());
        BoxTemplate boxTemplate = boxTemplateMapper.selectById(vehicleInfoVO.getBoxTemplateId());
        if (Objects.nonNull(boxTemplate)) {
            DeviceHardwareModelVO deviceHardwareModelVO = new DeviceHardwareModelVO();
            deviceHardwareModelVO.setHardwareModelId(boxTemplate.getHardwareModelId());
            deviceHardwareModelVO.setNum(1);
            deviceHardwareModelVO.setSource(DeviceHardwareSourceEnum.DEVICE.getValue());
            deviceHardwareModelVOList.add(deviceHardwareModelVO);
        }
        //重新添加
        addDeviceHardwareInfo(newDeviceBaseInfo.getId(), deviceHardwareModelVOList);

        if (!CollectionUtils.isEmpty(vehicleInfoVO.getCardNoList())) {
            //先将旧的删除
            LambdaQueryWrapper<VehicleCardNo> vehicleCardNoLambdaQueryWrapper = new LambdaQueryWrapper<>();
            vehicleCardNoLambdaQueryWrapper.eq(VehicleCardNo::getVehicleId, vehicleInfoVO.getId());
            vehicleCardNoMapper.delete(vehicleCardNoLambdaQueryWrapper);
            //再添加
            for (VehicleCardNoVO vehicleCardNoVO : vehicleInfoVO.getCardNoList()) {
                VehicleCardNo vehicleCardNo = new VehicleCardNo();
                vehicleCardNo.setEnable(vehicleCardNoVO.getEnable());
                vehicleCardNo.setVehicleId(vehicleInfoVO.getId());
                vehicleCardNo.setTelecomOperator(vehicleCardNoVO.getTelecomOperator());
                vehicleCardNo.setCardNo(vehicleCardNoVO.getCardNo());
                vehicleCardNo.setCreateUser(username);
                vehicleCardNo.setModifyUser(username);
                vehicleCardNoMapper.insert(vehicleCardNo);
            }
        }
        //提交完成后发送消息
        if (TransactionSynchronizationManager.isSynchronizationActive()) {
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    if (ProductTypeEnum.VEHICLE.getValue().equals(oldDeviceBaseInfo.getProductType())) {
                        //如果修改了车牌号,需要发送kafka
                        if (!oldDeviceBaseInfo.getName().equals(vehicleInfoVO.getName())) {
                            //发送变更消息
                            List<String> oldVehicleNumberList = new ArrayList<>();
                            oldVehicleNumberList.add(oldDeviceBaseInfo.getName());
                            log.info("车辆编辑,修改车牌号人车关系发生变化消息发送->旧车辆列表:{},旧站点:{},新站点:{}", oldVehicleNumberList, oldDeviceBaseInfo.getStationBaseId(), null);
                            jmqDeviceProducer.sendUserVehicleChangeMessage(oldVehicleNumberList, oldDeviceBaseInfo.getStationBaseId(), null);
                            List<String> newVehicleNumberList = new ArrayList<>();
                            newVehicleNumberList.add(newDeviceBaseInfo.getName());
                            log.info("车辆编辑,修改车牌号人车关系发生变化消息发送->新车辆列表:{},旧站点:{},新站点:{}", newVehicleNumberList, null, newDeviceBaseInfo.getStationBaseId());
                            jmqDeviceProducer.sendUserVehicleChangeMessage(newVehicleNumberList, null, newDeviceBaseInfo.getStationBaseId());
                            jmqDeviceProducer.sendMetadataVehicleMessageWidthDeviceInfo(OperationTypeEnum.DELETE, oldDeviceBaseInfo);
                        }
                    }
                    //车辆信息变更消息
                    jmqDeviceProducer.sendMetadataVehicleMessage(OperationTypeEnum.EDIT, newDeviceBaseInfo.getName());

                    //发送箱体变更消息
                    if (!Objects.equals(vehicleInfoVO.getBoxTemplateId(), oldDeviceBaseInfo.getBoxTemplateId())) {
                        jmqBoxProducer.sendMetadataBoxMessage(newDeviceBaseInfo.getBoxId(), newDeviceBaseInfo.getName(), newDeviceBaseInfo.getProductType(), newDeviceBaseInfo.getId());
                    }
                }
            });
        }
        return BaseResponseEnum.SUCCESS;
    }

    /**
     * 过滤掉无效的硬件型号
     * @param deviceHardwareModelVOList
     */
    private void removeUselessHardwareModel(List<DeviceHardwareModelVO> deviceHardwareModelVOList){
        if(!CollectionUtils.isEmpty(deviceHardwareModelVOList)){
            deviceHardwareModelVOList.removeIf(hardwareModelVO -> Objects.isNull(hardwareModelVO.getHardwareModelId()));
        }
    }

    /**
     * 检测无人车的硬件型号
     * @param vehicleInfoAddVO
     * @return
     */
    private boolean checkHardwareModel(VehicleInfoAddVO vehicleInfoAddVO){
        if(vehicleInfoAddVO.getProductType().equals(ProductTypeEnum.VEHICLE.getValue()) && SupplierEnum.JD.getValue().equals(vehicleInfoAddVO.getSupplier())){
            //无人车，需要判断硬件型号不能为空
            if(CollectionUtils.isEmpty(vehicleInfoAddVO.getDeviceHardwareModelList())) {
                return true;
            }
            List<DeviceHardwareModelVO> deviceHardwareModelVOList = vehicleInfoAddVO.getDeviceHardwareModelList();
            for(int i = 0; i < deviceHardwareModelVOList.size(); i++){
                if(StringUtils.isBlank(deviceHardwareModelVOList.get(i).getSource()) || Objects.isNull(deviceHardwareModelVOList.get(i).getHardwareModelId())){
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 校验是否存在车辆车架号
     */
    private boolean checkExistVehicleSerialNo(String serialNo) {
        LambdaQueryWrapper<DeviceBaseInfo> vehicleLambdaQueryWrapper = new LambdaQueryWrapper<>();
        vehicleLambdaQueryWrapper.eq(DeviceBaseInfo::getSerialNo, serialNo);
        List<DeviceBaseInfo> vehicleSerialNoList = deviceBaseInfoMapper.selectList(vehicleLambdaQueryWrapper);
        if (!CollectionUtils.isEmpty(vehicleSerialNoList)) {
            log.warn("该车架号[{}]系统已存在,绑定车辆ID[{}],操作失败!", serialNo, vehicleSerialNoList.get(0).getId());
            return true;
        }
        return false;
    }

    /**
     * 校验更新时是否存在车辆车架号
     */
    private boolean checkUpdateExistVehicleSerialNo(Integer id, String serialNo) {
        DeviceBaseInfo deviceBaseInfo = deviceBaseInfoMapper.selectById(id);
        if (Objects.equals(deviceBaseInfo.getSerialNo(), serialNo)) {
            return false;
        }
        return checkExistVehicleSerialNo(serialNo);
    }

    /**
     * 校验是否存在车辆车牌号
     */
    private boolean checkExistDeviceName(String name) {
        LambdaQueryWrapper<DeviceBaseInfo> vehicleLambdaQueryWrapper = new LambdaQueryWrapper<>();
        vehicleLambdaQueryWrapper.eq(DeviceBaseInfo::getName, name);
        List<DeviceBaseInfo> vehicleInfoLicenseList = deviceBaseInfoMapper.selectList(vehicleLambdaQueryWrapper);
        if (!CollectionUtils.isEmpty(vehicleInfoLicenseList)) {
            log.warn("该车牌号[{}]系统已存在,绑定车辆ID[{}],操作失败!", name, vehicleInfoLicenseList.get(0).getId());
            return true;
        }
        return false;
    }

    /**
     * 校验更新时是否存在车辆车牌号
     */
    private boolean checkUpdateExistVehicleLicense(Integer id, String name) {
        DeviceBaseInfo deviceBaseInfo = deviceBaseInfoMapper.selectById(id);
        if (Objects.equals(deviceBaseInfo.getName(), name)) {
            return false;
        }
        return checkExistDeviceName(name);
    }

    /**
     * 保存箱体和格口信息
     */
    private Box saveBoxAndGrid(Integer boxTemplateId, DeviceBaseInfo deviceBaseInfo) {
        String username = JsfLoginUtil.getUsername();
        // 保存箱体信息
        Box box = new Box();
        if (StringUtils.isEmpty(deviceBaseInfo.getSerialNo())) {
            // update时车架号不可变,不传,所以需要数据库读取
            DeviceBaseInfo deviceDb = deviceBaseInfoMapper.selectById(deviceBaseInfo.getId());
            box.setSerialNo(deviceDb.getSerialNo());
        } else {
            box.setSerialNo(deviceBaseInfo.getSerialNo());
        }
        BoxTemplate boxTemplate = boxTemplateMapper.selectById(boxTemplateId);
        if (Objects.nonNull(boxTemplate)) {
            box.setDescription(boxTemplate.getDescription());
            box.setBaudRate(boxTemplate.getBaudRate());
            box.setProtocol(boxTemplate.getProtocol());
            box.setGridNum(boxTemplate.getGridNum());
            box.setModel(boxTemplate.getHardwareModelModel());
            box.setDriverType(boxTemplate.getDriverType());
            box.setDeviceId(boxTemplate.getDeviceId());
            box.setBaudRate(boxTemplate.getBaudRate());
            box.setLeftBoxColumnNum(boxTemplate.getLeftBoxColumnNum());
            box.setRightBoxColumnNum(boxTemplate.getRightBoxColumnNum());
            box.setEnable(boxTemplate.getEnable());
            box.setCreateUser(username);
            box.setModifyUser(username);
            boxMapper.insert(box);
        }
        // 保存箱体格口模板
        LambdaQueryWrapper<BoxGridTemplate> boxGridTemplateLambdaQueryWrapper = new LambdaQueryWrapper<>();
        boxGridTemplateLambdaQueryWrapper.eq(BoxGridTemplate::getBoxTemplateId, boxTemplateId);
        List<BoxGridTemplate> boxGridTemplateList = boxGridTemplateMapper.selectList(boxGridTemplateLambdaQueryWrapper);
        for (BoxGridTemplate boxGridTemplate : boxGridTemplateList) {
            Grid grid = new Grid();
            grid.setGridNo(boxGridTemplate.getGridNo());
            grid.setLength(boxGridTemplate.getLength());
            grid.setWidth(boxGridTemplate.getWidth());
            grid.setHeight(boxGridTemplate.getHeight());
            grid.setSide(boxGridTemplate.getSide());
            grid.setLockNo(boxGridTemplate.getLockNo());
            grid.setEnable(boxGridTemplate.getEnable());
            grid.setBoardNo(boxGridTemplate.getBoardNo());
            grid.setBoxId(box.getId());
            grid.setCreateUser(username);
            grid.setModifyUser(username);
            gridMapper.insert(grid);
            // 保存小格口模板
            LambdaQueryWrapper<BoxGridPalletTemplate> boxGridPalletTemplateLambdaQueryWrapper = new LambdaQueryWrapper<>();
            boxGridPalletTemplateLambdaQueryWrapper.eq(BoxGridPalletTemplate::getGridId, boxGridTemplate.getId());
            boxGridPalletTemplateLambdaQueryWrapper.eq(BoxGridPalletTemplate::getEnable, EnableEnum.ENABLE.getEnable());
            List<BoxGridPalletTemplate> palletTemplates = boxGridPalletTemplateMapper.selectList(boxGridPalletTemplateLambdaQueryWrapper);
            for (BoxGridPalletTemplate palletTemplate : palletTemplates) {
                Pallet pallet = new Pallet();
                pallet.setGridId(grid.getId());
                pallet.setName(palletTemplate.getName());
                pallet.setEnable(EnableEnum.ENABLE.getEnable());
                pallet.setCreateUser(username);
                pallet.setModifyUser(username);
                palletMapper.insert(pallet);
            }
        }
        return box;
    }

    /**
     * 设备信息PO转换为DTO.
     *
     * @param deviceBaseInfo 设备信息.
     */
    private VehicleInfoDTO convertPoToDto(DeviceBaseInfo deviceBaseInfo) {
        if (deviceBaseInfo == null) {
            return null;
        }
        VehicleInfoDTO vehicleInfoDTO = new VehicleInfoDTO();
        vehicleInfoDTO.setId(deviceBaseInfo.getId());
        vehicleInfoDTO.setName(deviceBaseInfo.getName());
        vehicleInfoDTO.setSerialNo(deviceBaseInfo.getSerialNo());
        vehicleInfoDTO.setProductType(deviceBaseInfo.getProductType());
        vehicleInfoDTO.setProductTypeName(ProductTypeEnum.getNameByValue(deviceBaseInfo.getProductType()));
        vehicleInfoDTO.setSupplier(deviceBaseInfo.getSupplier());
        vehicleInfoDTO.setSupplierName(SupplierEnum.getNameByValue(deviceBaseInfo.getSupplier()));
        vehicleInfoDTO.setBusinessType(deviceBaseInfo.getBusinessType());
        vehicleInfoDTO.setBusinessTypeName(VehicleBusinessTypeEnum.getNameByValue(deviceBaseInfo.getBusinessType()));
        vehicleInfoDTO.setAndroidDeviceId(deviceBaseInfo.getAndroidDeviceId());
        vehicleInfoDTO.setModifyUser(deviceBaseInfo.getModifyUser());
        vehicleInfoDTO.setModifyTime(deviceBaseInfo.getModifyTime());

        if (StringUtils.isNotBlank(deviceBaseInfo.getHardwareStatus())) {
            vehicleInfoDTO.setHardwareStatusName(VehicleHardwareStatusEnum.getNameByValue(deviceBaseInfo.getHardwareStatus()));
        }
        if (StringUtils.isNotBlank(deviceBaseInfo.getCheckStatus())) {
            vehicleInfoDTO.setCheckStatusName(VehicleCheckStatusEnum.getNameByValue(deviceBaseInfo.getCheckStatus()));
        }
        Box box = boxMapper.selectById(deviceBaseInfo.getBoxId());
        if (Objects.nonNull(box)) {
            vehicleInfoDTO.setGridNum(box.getGridNum());
        }
        //系统标签信息
        Integer tag = deviceBaseInfo.getTag();
        List<Integer> tagList = new ArrayList<>();
        List<String> tagNameList = new ArrayList<>();
        if (tag != null && tag > 0) {
            for (TagEnum tagEnum : TagEnum.values()) {
                Integer value = tagEnum.getValue();
                if ((tag & 1 << value) > 0) {
                    tagList.add(value);
                    tagNameList.add(tagEnum.getName());
                }
            }
        }
        vehicleInfoDTO.setTagList(tagList);
        vehicleInfoDTO.setTagName(CollectionUtils.isEmpty(tagNameList) ? null : String.join(",", tagNameList));

        //获取车型
        DeviceTypeBaseInfo deviceTypeBaseInfo = deviceTypeBaseInfoMapper.selectById(deviceBaseInfo.getDeviceTypeBaseId());
        if (Objects.nonNull(deviceTypeBaseInfo)) {
            vehicleInfoDTO.setDeviceTypeBaseId(deviceTypeBaseInfo.getId());
            vehicleInfoDTO.setDeviceTypeName(deviceTypeBaseInfo.getName());
        }

        //获取箱体模板信息
        BoxTemplate boxTemplate = boxTemplateMapper.selectById(deviceBaseInfo.getBoxTemplateId());
        if (Objects.nonNull(boxTemplate)) {
            vehicleInfoDTO.setBoxTemplateId(boxTemplate.getId());
            vehicleInfoDTO.setBoxTemplateName(boxTemplate.getName());
            vehicleInfoDTO.setHardwareModelName(boxTemplate.getHardwareModelName());
            vehicleInfoDTO.setHardwareModelModel(boxTemplate.getHardwareModelModel());
        }
        return vehicleInfoDTO;
    }

    /**
     * 通过设备信息ID获取物联网卡号列表.
     *
     * @param
     */
    private List<VehicleCardNoDTO> getCardNoListById(Integer deviceId) {
        List<VehicleCardNoDTO> operationVehicleCardNoList = new ArrayList<>();
        LambdaQueryWrapper<VehicleCardNo> vehicleCardNoLambdaQueryWrapper = new LambdaQueryWrapper<>();
        vehicleCardNoLambdaQueryWrapper.eq(VehicleCardNo::getVehicleId, deviceId);
        vehicleCardNoLambdaQueryWrapper.eq(VehicleCardNo::getEnable, EnableEnum.ENABLE.getEnable());
        List<VehicleCardNo> vehicleCardNoList = vehicleCardNoMapper.selectList(vehicleCardNoLambdaQueryWrapper);
        for (VehicleCardNo vehicleCardNo : vehicleCardNoList) {
            VehicleCardNoDTO vehicleCardNoDTO = new VehicleCardNoDTO();
            vehicleCardNoDTO.setCardNo(vehicleCardNo.getCardNo());
            vehicleCardNoDTO.setTelecomOperator(vehicleCardNo.getTelecomOperator());
            vehicleCardNoDTO.setEnable(vehicleCardNo.getEnable());
            vehicleCardNoDTO.setId(vehicleCardNo.getId());
            operationVehicleCardNoList.add(vehicleCardNoDTO);
            String operator = OperatorEnum.getNameByValue(vehicleCardNo.getTelecomOperator());
            if (operator != null) {
                vehicleCardNoDTO.setTelecomOperatorName(operator);
            }
        }
        return operationVehicleCardNoList;
    }

    /**
     * 根据车牌号获取车辆信息
     *
     * @param vehicleName
     * @return
     */
    public List<String> getByVehicleName(String vehicleName) {
        LambdaQueryWrapper<DeviceBaseInfo> vehicleLambdaQueryWrapper = new LambdaQueryWrapper<>();
        vehicleLambdaQueryWrapper.like(DeviceBaseInfo::getName, vehicleName);
        List<DeviceBaseInfo> deviceBaseInfoList = deviceBaseInfoMapper.selectList(vehicleLambdaQueryWrapper);
        List<String> deviceNameList = new ArrayList<>();
        for (DeviceBaseInfo device : deviceBaseInfoList) {
            deviceNameList.add(device.getName());
        }
        return deviceNameList;
    }

    /**
     * 6、解绑车辆安卓设备
     *
     * @param vehicleName
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BaseResponseEnum unbindAndroidDevice(String vehicleName) {
        LambdaUpdateWrapper<DeviceBaseInfo> vehicleUpdateWrapper = new LambdaUpdateWrapper<>();
        vehicleUpdateWrapper.eq(DeviceBaseInfo::getName, vehicleName);
        vehicleUpdateWrapper.set(DeviceBaseInfo::getAndroidDeviceId, null);
        int updateCount = deviceBaseInfoMapper.update(null, vehicleUpdateWrapper);
        if (updateCount != 1) {
            throw new AppRuntimeException(BaseResponseEnum.ERROR_VEHICLE_UNBIND_ANDROID_DEVICE.getMessage());
        }
        //提交完成后发送消息
        if (TransactionSynchronizationManager.isSynchronizationActive()) {
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    //车辆信息变化
                    jmqDeviceProducer.sendMetadataVehicleMessage(OperationTypeEnum.EDIT, vehicleName);
                }
            });
        }
        return BaseResponseEnum.SUCCESS;
    }

    /**
     * 7、获取车辆api key
     *
     * @param deviceId
     * @return
     */
    public Map<String, String> getVehicleApiKey(Integer deviceId) {
        DeviceBaseInfo deviceBaseInfo = deviceBaseInfoMapper.selectById(deviceId);
        if (Objects.isNull(deviceBaseInfo)) {
            return null;
        }
        Map<String, String> map = new HashMap<>();
        map.put("apikey", deviceBaseInfo.getApiKey());
        return map;
    }


    /**
     * 将所有车辆数据同步至oauth的client表
     *
     * @return
     */
    public BaseResponseEnum syncVehicleToOauthClient() {
        List<String> scopeList = new ArrayList<>();
        scopeList.add("default");
        List<Oauth2RegisteredClientAddVO> oauth2RegisteredClientAddVOList = deviceBaseInfoMapper.selectVehicleNameAndApiKey();
        oauth2RegisteredClientAddVOList.forEach(oauth2RegisteredClientAddVO -> oauth2RegisteredClientAddVO.setScopeList(scopeList));
        oauthServerFeignManager.registerOauthClient(oauth2RegisteredClientAddVOList);
        return BaseResponseEnum.SUCCESS;
    }

    /**
     * 根据车牌号获取id,车架号,位置信息
     *
     * @param vehicleName
     * @return
     */
    public VehicleInfoToRequireDTO getVehicleInfoByName(String vehicleName) {
        log.info("根据车牌号获取id,车架号,位置信息入参:{}", vehicleName);
        LambdaQueryWrapper<DeviceBaseInfo> vehicleLambdaQueryWrapper = new LambdaQueryWrapper<>();
        vehicleLambdaQueryWrapper.eq(DeviceBaseInfo::getName, vehicleName);
        DeviceBaseInfo device = deviceBaseInfoMapper.selectOne(vehicleLambdaQueryWrapper);
        if (Objects.isNull(device)) {
            throw new AppRuntimeException("该车牌号无法获取到对应的车辆信息,车辆不存在");
        }
        VehicleInfoToRequireDTO vehicleInfoToRequireDTO = new VehicleInfoToRequireDTO();
        vehicleInfoToRequireDTO.setId(device.getId());
        vehicleInfoToRequireDTO.setName(device.getName());
        vehicleInfoToRequireDTO.setSerialNo(device.getSerialNo());
        if (Objects.nonNull(device.getStationBaseId())) {
            //获取站点信息
            StationBaseInfo stationBaseInfo = stationBaseInfoMapper.selectById(device.getStationBaseId());
            AddressInfo addressInfo = cityMapper.selectAddressInfoById(stationBaseInfo.getCityId());
            vehicleInfoToRequireDTO.setStationInfo(addressInfo.getCountryName() + ">" + addressInfo.getStateName() + ">" + addressInfo.getCityName() + ">" + stationBaseInfo.getName());
        }
        return vehicleInfoToRequireDTO;
    }

    /**
     * 获取车辆硬件信息
     *
     * @param deviceBaseId
     * @return
     */
    public List<HardwareModelInfoDTO> getHardwareModelInfoOfVehicle(Integer deviceBaseId) {
        DeviceBaseInfo deviceBaseInfo = deviceBaseInfoMapper.selectById(deviceBaseId);
        if (deviceBaseInfo == null) {
            throw new AppRuntimeException("该车牌号无法获取到对应的车辆信息,车辆不存在");
        }
        List<HardwareModelInfoDTO> hardwareModelInfoDTOList = new ArrayList<>();

        LambdaQueryWrapper<DeviceHardwareInfo> deviceHardwareInfoLambdaQueryWrapper = new LambdaQueryWrapper<>();
        deviceHardwareInfoLambdaQueryWrapper.eq(DeviceHardwareInfo::getDeviceBaseId, deviceBaseId);
        List<DeviceHardwareInfo> deviceHardwareInfoList = deviceHardwareInfoMapper.selectList(deviceHardwareInfoLambdaQueryWrapper);
        if (CollectionUtils.isEmpty(deviceHardwareInfoList)) {
            return hardwareModelInfoDTOList;
        }
        Set<Integer> deviceHardwareModelIdSet = deviceHardwareInfoList.stream().map(DeviceHardwareInfo::getHardwareModelId).collect(Collectors.toSet());
        LambdaQueryWrapper<HardwareModel> hardwareModelLambdaQueryWrapper = new LambdaQueryWrapper<>();
        hardwareModelLambdaQueryWrapper.in(HardwareModel::getId, deviceHardwareModelIdSet);
        List<HardwareModel> hardwareModelList = hardwareModelMapper.selectList(hardwareModelLambdaQueryWrapper);
        Map<Integer, HardwareModel> hardwareModelIdMap = hardwareModelList.stream().collect(Collectors.toMap(HardwareModel::getId, hardwareModel -> hardwareModel));
        deviceHardwareInfoList.forEach(deviceHardwareInfo -> {
            HardwareModelInfoDTO hardwareModelInfoDTO = new HardwareModelInfoDTO();
            HardwareModel hardwareModel = hardwareModelIdMap.get(deviceHardwareInfo.getHardwareModelId());
            if (Objects.isNull(hardwareModel)) {
                return;
            }
            hardwareModelInfoDTO.setHardwareModelId(deviceHardwareInfo.getHardwareModelId());
            hardwareModelInfoDTO.setHardwareTypeId(hardwareModel.getHardwareTypeId());
            hardwareModelInfoDTO.setHardwareModelModel(hardwareModel.getModel());
            hardwareModelInfoDTO.setHardwareModelName(hardwareModel.getName());
            hardwareModelInfoDTO.setIsOfVehicleType(DeviceHardwareSourceEnum.DEVICE.getValue().equals(deviceHardwareInfo.getSource()) ? 0 : 1);
            hardwareModelInfoDTOList.add(hardwareModelInfoDTO);
        });
        return hardwareModelInfoDTOList;
    }

    /**
     * 获取车辆列表
     */
    public List<String> getVehicleList() {
        List<DeviceBaseInfo> vehicles = deviceBaseInfoMapper.selectList(new LambdaQueryWrapper<>());
        List<String> result = vehicles.stream().map(DeviceBaseInfo::getName).collect(Collectors.toList());
        return result;
    }

    /**
     * 判断车辆是否位于同一公司
     *
     * @param checkVehicleVO
     * @return
     */
    public Boolean checkVehicleInSameCompany(CheckVehicleVO checkVehicleVO) {
        LambdaQueryWrapper<CompanyStationInfo> companyStationInfoLambdaQueryWrapper = new LambdaQueryWrapper<>();
        companyStationInfoLambdaQueryWrapper.in(CompanyStationInfo::getStationId, checkVehicleVO.getStationBaseIdList());
        companyStationInfoLambdaQueryWrapper.select(CompanyStationInfo::getCompanyNumber);
        List<CompanyStationInfo> companyStationInfos = companyStationInfoMapper.selectList(companyStationInfoLambdaQueryWrapper);
        Set<String> companyNumberSize = companyStationInfos.stream().map(CompanyStationInfo::getCompanyNumber).collect(Collectors.toSet());
        if (companyNumberSize.size() != 1) {
            return false;
        }
        //判断是否有站点(车)没有绑定公司
        companyStationInfoLambdaQueryWrapper.clear();
        companyStationInfoLambdaQueryWrapper.in(CompanyStationInfo::getStationId, checkVehicleVO.getStationBaseIdList());
        Long count = companyStationInfoMapper.selectCount(companyStationInfoLambdaQueryWrapper);
        //对站点进行去重
        Set<Integer> stationCount = new HashSet<>(checkVehicleVO.getStationBaseIdList());
        return count != null && count == stationCount.size();
    }

    /**
     * 添加硬件型号到设备硬件表
     *
     * @param baseId
     * @param list
     */
    private void addDeviceHardwareInfo(Integer baseId, List<DeviceHardwareModelVO> list) {
        String username = JsfLoginUtil.getUsername();
        //添加关联的硬件型号
        if (!CollectionUtils.isEmpty(list)) {
            removeUselessHardwareModel(list);
            list.forEach(deviceHardwareModelVO -> {
                DeviceHardwareInfo deviceHardwareInfo = new DeviceHardwareInfo();
                deviceHardwareInfo.setDeviceBaseId(baseId);
                deviceHardwareInfo.setHardwareModelId(deviceHardwareModelVO.getHardwareModelId());
                deviceHardwareInfo.setHardwareNumber(deviceHardwareModelVO.getHardwareNumber());
                deviceHardwareInfo.setNum(Objects.isNull(deviceHardwareModelVO.getNum()) ? 1 : deviceHardwareModelVO.getNum());
                deviceHardwareInfo.setSource(Objects.isNull(deviceHardwareModelVO.getSource()) ? DeviceHardwareSourceEnum.DEVICE.getValue() : deviceHardwareModelVO.getSource());
                deviceHardwareInfo.setCreateUser(username);
                deviceHardwareInfo.setModifyUser(username);
                deviceHardwareInfoMapper.insert(deviceHardwareInfo);
            });
        }
    }

    public BaseResponseEnum importDeviceIntoPermission() {
        LambdaQueryWrapper<DeviceBaseInfo> deviceBaseInfoLambdaQueryWrapper = new LambdaQueryWrapper<>();
        deviceBaseInfoLambdaQueryWrapper.eq(DeviceBaseInfo::getProductType, ProductTypeEnum.VEHICLE.getValue());
        deviceBaseInfoLambdaQueryWrapper.select(DeviceBaseInfo::getName, DeviceBaseInfo::getProductType, DeviceBaseInfo::getApiKey, DeviceBaseInfo::getCreateUser);
        List<DeviceBaseInfo> deviceBaseInfoList = deviceBaseInfoMapper.selectList(deviceBaseInfoLambdaQueryWrapper);
        deviceBaseInfoList.forEach(deviceBaseInfo -> {
            ClientBasicImportVO clientBasicImportVO = new ClientBasicImportVO();
            clientBasicImportVO.setClientId(deviceBaseInfo.getName());
            clientBasicImportVO.setClientSecret(deviceBaseInfo.getApiKey());
            //设置角色组编号
            List<String> roleGroupNumberList = new ArrayList<>();
            roleGroupNumberList.add(deviceRoleGroupProperties.getVehicle());
            clientBasicImportVO.setRoleGroupNumberList(roleGroupNumberList);
            clientBasicImportVO.setOperator(K2_MANAGEMENT_PLATFORM);
            try {
                permissionClientInfoBasicService.importDeviceInfo(clientBasicImportVO);
            } catch (Exception e) {
                log.error("device导入permission中client发生异常", e);
            }
        });
        return BaseResponseEnum.SUCCESS;
    }

    /**
     * @description 同步数据到通用设备管理平台
     * @param :
     * @return BaseResponseEnum
     * <AUTHOR>
     * @date 2024/12/31 16:13
     */
    public BaseResponseEnum importDeviceToIntelligentDevice() {
        LambdaQueryWrapper<DeviceBaseInfo> deviceBaseInfoLambdaQueryWrapper = new LambdaQueryWrapper<>();
        List<DeviceBaseInfo> deviceBaseInfoList = deviceBaseInfoMapper.selectList(deviceBaseInfoLambdaQueryWrapper);
        intelligentDeviceServerDeviceManager.importDeviceToIntelligentDevice(deviceBaseInfoList);
        return BaseResponseEnum.SUCCESS;
    }

    /**
     * @description 更改车牌号或apiKey
     * @param changeDeviceNameOrApiKeyVO:
     * @return BaseResponseEnum
     * <AUTHOR>
     * @date 2025/1/6 21:19
     */
    @Transactional(rollbackFor = Exception.class)
    public BaseResponseEnum changeDeviceNameOrApiKey(ChangeDeviceNameOrApiKeyVO changeDeviceNameOrApiKeyVO) {
        //获取旧设备信息
        LambdaQueryWrapper<DeviceBaseInfo> deviceBaseInfoLambdaQueryWrapper = new LambdaQueryWrapper<>();
        deviceBaseInfoLambdaQueryWrapper.eq(DeviceBaseInfo::getName, changeDeviceNameOrApiKeyVO.getOldDeviceName());
        deviceBaseInfoLambdaQueryWrapper.select(DeviceBaseInfo::getId, DeviceBaseInfo::getProductType, DeviceBaseInfo::getName, DeviceBaseInfo::getSerialNo);
        DeviceBaseInfo oldDeviceBaseInfo = deviceBaseInfoMapper.selectOne(deviceBaseInfoLambdaQueryWrapper);
        if (Objects.isNull(oldDeviceBaseInfo)) {
             throw new AppRuntimeException(BaseResponseEnum.ERROR_VEHICLE_IS_NOT_EXIST);
        }
        //判断新的车牌号是否存在
        if (StringUtils.isNotBlank(changeDeviceNameOrApiKeyVO.getNewDeviceName())) {
            LambdaQueryWrapper<DeviceBaseInfo> newDeviceBaseInfoLambdaQueryWrapper = new LambdaQueryWrapper<>();
            newDeviceBaseInfoLambdaQueryWrapper.eq(DeviceBaseInfo::getName, changeDeviceNameOrApiKeyVO.getNewDeviceName());
            if (deviceBaseInfoMapper.exists(newDeviceBaseInfoLambdaQueryWrapper)) {
                throw new AppRuntimeException(BaseResponseEnum.ERROR_DEVICE_NEW_DEVICE_NAME_EXIST);
            }
        }

        //设备名称+产品标识
        String productKey;
        String oldIntelligentDeviceName;
        String newIntelligentDeviceName = null;
        if (ProductTypeEnum.VEHICLE.getValue().equals(oldDeviceBaseInfo.getProductType())) {
            //如果是无人车使用车牌号注册
            oldIntelligentDeviceName = oldDeviceBaseInfo.getName();
            //无人车产品标识
            productKey = productKeyProperties.getVehicle();
            //要修改的车牌号
            newIntelligentDeviceName = changeDeviceNameOrApiKeyVO.getNewDeviceName();
        } else if (ProductTypeEnum.ROBOT.getValue().equals(oldDeviceBaseInfo.getProductType())) {
            //如果是小红车使用车架号注册
            oldIntelligentDeviceName = oldDeviceBaseInfo.getSerialNo();
            //小红车机器人产品标识
            productKey = productKeyProperties.getRobot();
            //小红车是用车架号注册的,所以不需要修改车牌号
        } else {
            //如果是多合一使用车架号注册
            oldIntelligentDeviceName = oldDeviceBaseInfo.getSerialNo();
            //多合一机器人产品标识
            productKey = productKeyProperties.getIntegrate();
            //多合一是用车架号注册的,所以不需要修改车牌号
        }

        //调用通用设备平台,修改设备名称或apiKey
        intelligentDeviceServerDeviceManager.changeDeviceNameOrApiKey(productKey, oldIntelligentDeviceName, newIntelligentDeviceName, changeDeviceNameOrApiKeyVO.getApiKey());

        //更新数据
        if (StringUtils.isNotBlank(changeDeviceNameOrApiKeyVO.getNewDeviceName())) {
            //修改车牌号
            //修改device_base_info表
            DeviceBaseInfo updateDeviceBaseInfo = new DeviceBaseInfo();
            updateDeviceBaseInfo.setId(oldDeviceBaseInfo.getId());
            updateDeviceBaseInfo.setName(changeDeviceNameOrApiKeyVO.getNewDeviceName());
            deviceBaseInfoMapper.updateById(updateDeviceBaseInfo);
            //修改vehicle_conf_info表

            //修改vehicle_conf_history_info_detail表

        }
        if (StringUtils.isNotBlank(changeDeviceNameOrApiKeyVO.getApiKey())) {
            //修改apikey
            DeviceBaseInfo updateDeviceBaseInfo = new DeviceBaseInfo();
            updateDeviceBaseInfo.setId(oldDeviceBaseInfo.getId());
            updateDeviceBaseInfo.setApiKey(changeDeviceNameOrApiKeyVO.getApiKey());
            deviceBaseInfoMapper.updateById(updateDeviceBaseInfo);
        }
        return BaseResponseEnum.SUCCESS;
    }

    /**
     * @description 查询站点下的或指定设备的位置简要信息列表
     * @param deviceAddressBriefInfoVO:
     * @return List<DeviceAddressBriefInfoDTO>
     * <AUTHOR>
     * @date 2025/3/4 20:06
     */
    public List<DeviceAddressBriefInfoDTO> getDeviceAddressBriefInfoList(DeviceAddressBriefInfoVO deviceAddressBriefInfoVO) {
        if (StringUtils.isBlank(deviceAddressBriefInfoVO.getDeviceName()) && Objects.isNull(deviceAddressBriefInfoVO.getStationBaseId())) {
            throw new AppRuntimeException(BaseResponseEnum.PARAM_ERROR);
        }
        LambdaQueryWrapper<DeviceBaseInfo> deviceBaseInfoLambdaQueryWrapper = new LambdaQueryWrapper<>();
        deviceBaseInfoLambdaQueryWrapper.eq(Objects.nonNull(deviceAddressBriefInfoVO.getStationBaseId()), DeviceBaseInfo::getStationBaseId, deviceAddressBriefInfoVO.getStationBaseId());
        deviceBaseInfoLambdaQueryWrapper.eq(StringUtils.isNotBlank(deviceAddressBriefInfoVO.getDeviceName()), DeviceBaseInfo::getName, deviceAddressBriefInfoVO.getDeviceName());
        deviceBaseInfoLambdaQueryWrapper.select(DeviceBaseInfo::getName, DeviceBaseInfo::getSerialNo, DeviceBaseInfo::getStationBaseId);
        List<DeviceBaseInfo> deviceBaseInfoList = deviceBaseInfoMapper.selectList(deviceBaseInfoLambdaQueryWrapper);
        //获取省市站信息
        Map<String, AddressInfoDTO> cityIdAddressInfoMap = new HashMap<>();
        Map<Integer, StationBaseInfo> stationIdCityMap = new HashMap<>();
        Set<Integer> stationIdSet = deviceBaseInfoList.stream().map(DeviceBaseInfo::getStationBaseId).collect(Collectors.toSet());
        if (!CollectionUtils.isEmpty(stationIdSet)) {
            //查询站点的城市id
            LambdaQueryWrapper<StationBaseInfo> stationBaseInfoLambdaQueryWrapper = new LambdaQueryWrapper<>();
            stationBaseInfoLambdaQueryWrapper.in(StationBaseInfo::getId, stationIdSet);
            stationBaseInfoLambdaQueryWrapper.select(StationBaseInfo::getId, StationBaseInfo::getName, StationBaseInfo::getCityId);
            List<StationBaseInfo> stationBaseInfoList = stationBaseInfoMapper.selectList(stationBaseInfoLambdaQueryWrapper);
            if (!CollectionUtils.isEmpty(stationBaseInfoList)) {
                stationIdCityMap = stationBaseInfoList.stream().collect(Collectors.toMap(StationBaseInfo::getId, StationBaseInfo -> StationBaseInfo));
                Set<Object> cityId = stationBaseInfoList.stream().map(StationBaseInfo::getCityId).map(String::valueOf).collect(Collectors.toSet());
                cityIdAddressInfoMap = cityManager.getAddress(cityId);
            }
        }
        //构造返回数据
        List<DeviceAddressBriefInfoDTO> deviceAddressBriefInfoDTOList = new ArrayList<>();
        for (DeviceBaseInfo deviceBaseInfo : deviceBaseInfoList) {
            DeviceAddressBriefInfoDTO deviceAddressBriefInfoDTO = new DeviceAddressBriefInfoDTO();
            deviceAddressBriefInfoDTO.setDeviceName(deviceBaseInfo.getName());
            deviceAddressBriefInfoDTO.setSerialNo(deviceBaseInfo.getSerialNo());
            StationBaseInfo stationBaseInfo = stationIdCityMap.get(deviceBaseInfo.getStationBaseId());
            if (Objects.nonNull(stationBaseInfo)) {
                deviceAddressBriefInfoDTO.setStationName(stationBaseInfo.getName());
                AddressInfoDTO addressInfoDTO = cityIdAddressInfoMap.get(String.valueOf(stationBaseInfo.getCityId()));
                if (Objects.nonNull(addressInfoDTO)) {
                    deviceAddressBriefInfoDTO.setCityName(addressInfoDTO.getCityName());
                    deviceAddressBriefInfoDTO.setProvinceName(addressInfoDTO.getProvinceName());
                }
            }
            deviceAddressBriefInfoDTOList.add(deviceAddressBriefInfoDTO);
        }

        return deviceAddressBriefInfoDTOList;
    }

    /**
     * @description 第三方车辆数据导入,字符串格式:车号,站点id,产品类型,供应商,设备型号,车型,车辆到站时间,车辆交付时间;
     * @param thirdPartDeviceInfoStr:
     * @return HttpResult<Void>
     * <AUTHOR>
     * @date 2025/3/31 17:26
     */
    public void exportThirdPartDeviceInfo(String thirdPartDeviceInfoStr) {
        String[] thirdPartDeviceInfoArr = thirdPartDeviceInfoStr.split(";");
        for (String thirdPartDeviceInfo : thirdPartDeviceInfoArr) {
            try {
                String[] thirdPartDeviceParams = thirdPartDeviceInfo.split(",");
                 //新建车辆
                VehicleInfoAddVO vehicleInfoAddVO = new VehicleInfoAddVO();
                vehicleInfoAddVO.setName(thirdPartDeviceParams[0]);
                vehicleInfoAddVO.setStationBaseId(Integer.valueOf(thirdPartDeviceParams[1]));
                vehicleInfoAddVO.setProductType(thirdPartDeviceParams[2]);
                vehicleInfoAddVO.setSupplier(thirdPartDeviceParams[3]);
                vehicleInfoAddVO.setBusinessType(thirdPartDeviceParams[4]);
                vehicleInfoAddVO.setDeviceTypeBaseId(Integer.valueOf(thirdPartDeviceParams[5]));
                //补充硬件信息列表
                List<DeviceHardwareModelVO> deviceHardwareModelList = new ArrayList<>();
                //查询车型硬件信息
                LambdaQueryWrapper<DeviceTypeHardwareInfo> deviceTypeHardwareInfoLambdaQueryWrapper = new LambdaQueryWrapper<>();
                deviceTypeHardwareInfoLambdaQueryWrapper.eq(DeviceTypeHardwareInfo::getDeviceTypeBaseId, vehicleInfoAddVO.getDeviceTypeBaseId());
                List<DeviceTypeHardwareInfo> deviceTypeHardwareInfoList = deviceTypeHardwareInfoMapper.selectList(deviceTypeHardwareInfoLambdaQueryWrapper);
                deviceTypeHardwareInfoList.forEach(deviceTypeHardwareInfo -> {
                    DeviceHardwareModelVO deviceHardwareModelVO = new DeviceHardwareModelVO();
                    deviceHardwareModelVO.setHardwareModelId(deviceTypeHardwareInfo.getHardwareModelId());
                    deviceHardwareModelVO.setSource("device_type");
                    deviceHardwareModelVO.setNum(deviceTypeHardwareInfo.getNum());
                    deviceHardwareModelList.add(deviceHardwareModelVO);
                });
                vehicleInfoAddVO.setDeviceHardwareModelList(deviceHardwareModelList);
                add(vehicleInfoAddVO);

                //修改车辆首次到站时间、车辆到站时间、车辆交付时间
                LambdaUpdateWrapper<DeviceBaseInfo> deviceBaseInfoLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
                deviceBaseInfoLambdaUpdateWrapper.eq(DeviceBaseInfo::getName, thirdPartDeviceParams[0]);
                deviceBaseInfoLambdaUpdateWrapper.set(DeviceBaseInfo::getFirstArriveTime, thirdPartDeviceParams[6]);
                deviceBaseInfoLambdaUpdateWrapper.set(DeviceBaseInfo::getArriveTime, thirdPartDeviceParams[6]);
                deviceBaseInfoLambdaUpdateWrapper.set(DeviceBaseInfo::getDeliveryTime, thirdPartDeviceParams[7]);
                deviceBaseInfoMapper.update(null, deviceBaseInfoLambdaUpdateWrapper);
            } catch (Exception e) {
                 log.error("第三方车辆数据导入失败, 参数:{}", thirdPartDeviceInfo, e);
            }
            log.info("第三方车辆数据导入成功, 参数:{}", thirdPartDeviceInfo);
        }
    }

    /**
     * @description 发送设备新增消息,消息格式:设备名称,站点id;
     * @param deviceAddInfoStr:
     * @return HttpResult<Void>
     * <AUTHOR>
     * @date 2025/3/31 22:48
     */
    public void sendDeviceInfoAddJmq(String deviceAddInfoStr) {
        String[] deviceAddInfoArr = deviceAddInfoStr.split(";");
        for (String deviceAddInfo : deviceAddInfoArr) {
            try {
                String[] deviceAddInfoParams = deviceAddInfo.split(",");
                List<String> vehicleNameList = new ArrayList<>();
                vehicleNameList.add(deviceAddInfoParams[0]);
                jmqDeviceProducer.sendUserVehicleChangeMessage(vehicleNameList,null, Integer.valueOf(deviceAddInfoParams[1]));
                //V2
                jmqDeviceProducer.sendMetadataVehicleMessage(OperationTypeEnum.ADD, deviceAddInfoParams[0]);
            } catch (Exception e) {
                log.error("发送设备新增消息失败, 参数:{}", deviceAddInfo, e);
            }
            log.info("发送设备新增消息成功, 参数:{}", deviceAddInfo);
        }
    }
}