/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.k2.management.service.technical;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jdx.k2.management.common.enums.EventNoticeUserEnum;
import com.jdx.k2.management.common.enums.EventSolutionEnum;
import com.jdx.k2.management.common.utils.PageUtil;
import com.jdx.k2.management.domain.dto.technical.PatternPlateRuleDTO;
import com.jdx.k2.management.domain.po.technical.PatternPlateRule;
import com.jdx.k2.management.domain.vo.technical.PatternPlateRuleAddVO;
import com.jdx.k2.management.domain.vo.technical.PatternPlateRuleListVO;
import com.jdx.k2.management.manager.feign.ShadowWebFeignManager;
import com.jdx.k2.management.repository.mapper.technical.PatternPlateRuleMapper;
import com.jdx.rover.common.domain.page.PageDTO;
import com.jdx.rover.common.domain.page.PageVO;
import com.jdx.rover.shadow.api.domain.vo.ShadowEventRuleConfigurationVO;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

/**
 * 影子系统-模板规则管理-service
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class PatternPlateRuleService {

    @Resource
    private PatternPlateRuleMapper patternPlateRuleMapper;
    @Resource
    private ShadowWebFeignManager shadowWebFeignManager;

    /**
     * 1、分页查询规则配置列表
     */
    public PageDTO<PatternPlateRuleDTO> getPatternPlateRulePageList(PageVO pageVO,
                                                                    PatternPlateRuleListVO patternPlateRuleListVO) {
        Page<PatternPlateRule> paramPage = new Page<>(pageVO.getPageNum(), pageVO.getPageSize());
        LambdaQueryWrapper<PatternPlateRule> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StringUtils.isNotEmpty(patternPlateRuleListVO.getName()),
                PatternPlateRule::getName, patternPlateRuleListVO.getName());
        queryWrapper.like(StringUtils.isNotEmpty(patternPlateRuleListVO.getEventNo()),
                PatternPlateRule::getEventNo, patternPlateRuleListVO.getEventNo());
        OrderItem orderItem = OrderItem.desc("id");
        paramPage.setOrders(Collections.singletonList(orderItem));
        IPage<PatternPlateRule> resultPage = patternPlateRuleMapper.selectPage(paramPage,
                queryWrapper);
        List<PatternPlateRuleDTO> patternPlateRuleResultList = new ArrayList<>();
        IPage<PatternPlateRuleDTO> iPage = new Page<>(resultPage.getCurrent(), resultPage.getSize(),
                resultPage.getTotal());
        if (CollectionUtils.isEmpty(resultPage.getRecords())) {
            return PageUtil.toPageDTO(iPage, null);
        }

        for (PatternPlateRule rule : resultPage.getRecords()) {
            patternPlateRuleResultList.add(domainToDTO(rule));
        }

        return PageUtil.toPageDTO(iPage, patternPlateRuleResultList);
    }

    /**
     * 2、新增规则
     */
    @Transactional(rollbackFor = Exception.class)
    public Integer addPatternPlateRule(PatternPlateRuleAddVO patternPlateRuleAddVO) {
        PatternPlateRule patternPlateRule = new PatternPlateRule();
        patternPlateRule.setName(patternPlateRuleAddVO.getName());
        patternPlateRule.setRuleType(patternPlateRuleAddVO.getRuleType());
        patternPlateRule.setDuration(patternPlateRuleAddVO.getDuration());
        patternPlateRule.setThresholdCount(patternPlateRuleAddVO.getThresholdCount());
        patternPlateRule.setThresholdTime(patternPlateRuleAddVO.getThresholdTime());
        patternPlateRule.setNoticeDd(patternPlateRuleAddVO.isNoticeDD());
        patternPlateRule.setNoticeMail(patternPlateRuleAddVO.isNoticeMail());
        List<String> noticeUser = patternPlateRuleAddVO.getNoticeUser();
        if (!CollectionUtils.isEmpty(noticeUser)) {
            patternPlateRule.setNoticeLeader(
                    noticeUser.contains(EventNoticeUserEnum.APPLICATION_LEADER.getValue()));
            patternPlateRule.setNoticeStationUser(
                    noticeUser.contains(EventNoticeUserEnum.STATION_LEADER.getValue()));
            patternPlateRule.setNoticeTechUser(
                    noticeUser.contains(EventNoticeUserEnum.TECHNICAL_SUPPORT.getValue()));
        }
        patternPlateRule.setNoticeGivenUser(patternPlateRuleAddVO.getGivenUser());
        patternPlateRule.setEventNo(patternPlateRuleAddVO.getEventNo());
        patternPlateRule.setSolution(patternPlateRuleAddVO.getSolution());
        patternPlateRule.setFrequencyLimit(patternPlateRuleAddVO.getFrequencyLimit());
        patternPlateRuleMapper.insert(patternPlateRule);
        sendAlarmRuleUpdateMsg(patternPlateRule);
        return patternPlateRule.getId();
    }

    /**
     * 3、查询规则列表
     */
    public List<PatternPlateRuleDTO> getPatternPlateRuleList(PatternPlateRuleListVO patternPlateRuleListVO) {
        LambdaQueryWrapper<PatternPlateRule> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StringUtils.isNotEmpty(patternPlateRuleListVO.getName()), PatternPlateRule::getName, patternPlateRuleListVO.getName());
        queryWrapper.like(StringUtils.isNotEmpty(patternPlateRuleListVO.getEventNo()), PatternPlateRule::getEventNo, patternPlateRuleListVO.getEventNo());
        List<PatternPlateRule> patternPlateRuleList = patternPlateRuleMapper.selectList(queryWrapper);
        List<PatternPlateRuleDTO> resultDTOList = new ArrayList<>();
        if (CollectionUtils.isEmpty(patternPlateRuleList)) {
            return resultDTOList;
        }
        return patternPlateRuleList.stream().map(this::domainToDTO).collect(Collectors.toList());
    }

    private PatternPlateRuleDTO domainToDTO(PatternPlateRule rule) {
        PatternPlateRuleDTO patternPlateRuleDTO = new PatternPlateRuleDTO();
        patternPlateRuleDTO.setId(rule.getId());
        patternPlateRuleDTO.setEventNo(rule.getEventNo());
        patternPlateRuleDTO.setName(rule.getName());
        patternPlateRuleDTO.setRuleType(rule.getRuleType());
        patternPlateRuleDTO.setDuration(rule.getDuration());
        patternPlateRuleDTO.setThresholdCount(rule.getThresholdCount());
        patternPlateRuleDTO.setThresholdTime(rule.getThresholdTime());
        patternPlateRuleDTO.setNoticeDD(rule.isNoticeDd());
        patternPlateRuleDTO.setNoticeMail(rule.isNoticeMail());
        List<String> noticeUser = new ArrayList<>();
        if (rule.isNoticeLeader()) {
            noticeUser.add(EventNoticeUserEnum.APPLICATION_LEADER.getValue());
        }
        if (rule.isNoticeStationUser()) {
            noticeUser.add(EventNoticeUserEnum.STATION_LEADER.getValue());
        }
        if (rule.isNoticeTechUser()) {
            noticeUser.add(EventNoticeUserEnum.TECHNICAL_SUPPORT.getValue());
        }
        patternPlateRuleDTO.setNoticeUser(noticeUser);
        patternPlateRuleDTO.setGivenUser(rule.getNoticeGivenUser());
        patternPlateRuleDTO.setFrequencyLimit(rule.getFrequencyLimit());
        patternPlateRuleDTO.setSolution(EventSolutionEnum.getByValue(rule.getSolution()).getName());
        patternPlateRuleDTO.setCreateTime(rule.getCreateTime());
        return patternPlateRuleDTO;
    }

    private void sendAlarmRuleUpdateMsg(PatternPlateRule patternPlateRule) {
        ShadowEventRuleConfigurationVO ruleDTO = new ShadowEventRuleConfigurationVO();
        ruleDTO.setId(patternPlateRule.getId());
        ruleDTO.setEventNo(patternPlateRule.getEventNo());
        ruleDTO.setName(patternPlateRule.getName());
        ruleDTO.setDuration(patternPlateRule.getDuration());
        ruleDTO.setFrequencyLimit(patternPlateRule.getFrequencyLimit());
        ruleDTO.setRuleType(patternPlateRule.getRuleType());
        ruleDTO.setThresholdCount(patternPlateRule.getThresholdCount());
        ruleDTO.setThresholdTime(patternPlateRule.getThresholdTime());
        ruleDTO.setNoticeDd(patternPlateRule.isNoticeDd());
        ruleDTO.setNoticeGivenUser(patternPlateRule.getNoticeGivenUser());
        ruleDTO.setNoticeLeader(patternPlateRule.isNoticeLeader());
        ruleDTO.setNoticeStationUser(patternPlateRule.isNoticeStationUser());
        ruleDTO.setNoticeTechUser(patternPlateRule.isNoticeTechUser());
        ruleDTO.setNoticeMail(patternPlateRule.isNoticeMail());
        ruleDTO.setStatus(patternPlateRule.isStatus());
        ruleDTO.setSolution(patternPlateRule.getSolution());
        shadowWebFeignManager.addRuleConfiguration(ruleDTO);
    }

}
