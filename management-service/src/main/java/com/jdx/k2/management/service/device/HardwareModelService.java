/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.k2.management.service.device;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jdx.k2.management.common.enums.base.BaseResponseEnum;
import com.jdx.k2.management.common.exception.AppRuntimeException;
import com.jdx.k2.management.common.utils.PageUtil;
import com.jdx.k2.management.domain.dto.device.HardwareModelDTO;
import com.jdx.k2.management.domain.po.device.HardwareModel;
import com.jdx.k2.management.domain.po.device.HardwareModelUsage;
import com.jdx.k2.management.domain.po.device.HardwareType;
import com.jdx.k2.management.domain.po.device.HardwareTypeUsage;
import com.jdx.k2.management.domain.po.device.Manufactory;
import com.jdx.k2.management.domain.po.device.Reference;
import com.jdx.k2.management.domain.vo.common.CommonVO;
import com.jdx.k2.management.domain.vo.common.UpdateEnableVO;
import com.jdx.k2.management.domain.vo.device.HardwareModelAddVO;
import com.jdx.k2.management.domain.vo.device.HardwareModelGetPageListVO;
import com.jdx.k2.management.domain.vo.device.HardwareModelVO;
import com.jdx.k2.management.domain.vo.device.ReferenceVO;
import com.jdx.k2.management.repository.mapper.device.HardwareModelMapper;
import com.jdx.k2.management.repository.mapper.device.HardwareModelUsageMapper;
import com.jdx.k2.management.repository.mapper.device.HardwareTypeMapper;
import com.jdx.k2.management.repository.mapper.device.HardwareTypeUsageMapper;
import com.jdx.k2.management.repository.mapper.device.ManufactoryMapper;
import com.jdx.k2.management.repository.mapper.device.ReferenceMapper;
import com.jdx.rover.common.domain.page.PageDTO;
import com.jdx.rover.common.domain.page.PageVO;
import com.jdx.rover.jsf.util.JsfLoginUtil;
import com.jdx.rover.metadata.api.domain.enums.common.EnableEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 运营管理系统硬件型号Service
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Slf4j
public class HardwareModelService {
    /**
     * 硬件型号数据库操作实体类.
     */
    @Resource
    private HardwareModelMapper hardwareModelMapper;

    /**
     * 硬件型号用途数据库操作实体类.
     */
    @Resource
    private HardwareModelUsageMapper hardwareModelUsageMapper;

    /**
     * 硬件类型数据库操作实体类
     */
    @Resource
    private HardwareTypeMapper hardwareTypeMapper;

    /**
     * 硬件类型用途数据库操作实体类
     */
    @Resource
    private HardwareTypeUsageMapper hardwareTypeUsageMapper;

    /**
     * 厂商数据库操作实体类
     */
    @Resource
    private ManufactoryMapper manufactoryMapper;

    @Resource
    private ReferenceMapper referenceMapper;

    /**
     * 1、分页查询硬件型号数据列表
     */
    public PageDTO<HardwareModelDTO> search(PageVO pageVO, HardwareModelGetPageListVO hardwareModelGetPageListVO) {
        IPage<HardwareModel> iPage = new Page<>(pageVO.getPageNum(), pageVO.getPageSize());
        LambdaQueryWrapper<HardwareModel> hardwareModelLambdaQueryWrapper = new LambdaQueryWrapper<>();
        hardwareModelLambdaQueryWrapper.like(hardwareModelGetPageListVO.getName() != null, HardwareModel::getName, hardwareModelGetPageListVO.getName());
        hardwareModelLambdaQueryWrapper.like(hardwareModelGetPageListVO.getModel() != null, HardwareModel::getModel, hardwareModelGetPageListVO.getModel());
        hardwareModelLambdaQueryWrapper.eq(hardwareModelGetPageListVO.getHardwareTypeId() != null, HardwareModel::getHardwareTypeId, hardwareModelGetPageListVO.getHardwareTypeId());
        hardwareModelLambdaQueryWrapper.eq(hardwareModelGetPageListVO.getEnable() != null, HardwareModel::getEnable, hardwareModelGetPageListVO.getEnable());
        hardwareModelLambdaQueryWrapper.orderByDesc(HardwareModel::getModifyTime);
        IPage<HardwareModel> hardwareModelIPage = hardwareModelMapper.selectPage(iPage, hardwareModelLambdaQueryWrapper);
        //封装DTO
        List<HardwareModelDTO> hardwareModelDTOList = hardwareModelIPage.getRecords().stream().map(hardwareModel -> {
            HardwareModelDTO hardwareModelDTO = new HardwareModelDTO();
            hardwareModelDTO.setHardwareTypeId(hardwareModel.getHardwareTypeId());
            hardwareModelDTO.setManufactoryId(hardwareModel.getManufactoryId());
            hardwareModelDTO.setCreateTime(hardwareModel.getCreateTime());
            hardwareModelDTO.setModifyTime(hardwareModel.getModifyTime());
            hardwareModelDTO.setModifyUser(hardwareModel.getModifyUser());
            hardwareModelDTO.setName(hardwareModel.getName());
            hardwareModelDTO.setId(hardwareModel.getId());
            hardwareModelDTO.setModel(hardwareModel.getModel());
            hardwareModelDTO.setEnable(hardwareModel.getEnable());
            hardwareModelDTO.setEnableName(EnableEnum.getNameByEnable(hardwareModel.getEnable()));
            List<Integer> hardwareModelUsageIdList = getUsageIdListById(hardwareModel.getId());
            hardwareModelDTO.setHardwareModelUsageIdList(hardwareModelUsageIdList);
            List<String> hardwareModelUsageNameList = new ArrayList<>();
            for (Integer hardwareModelUsageId : hardwareModelUsageIdList) {
                HardwareTypeUsage hardwareTypeUsage = hardwareTypeUsageMapper.selectById(hardwareModelUsageId);
                hardwareModelUsageNameList.add(hardwareTypeUsage.getUsageName());
            }
            hardwareModelDTO.setHardwareModelUsageNameList(hardwareModelUsageNameList);
            HardwareType hardwareType = hardwareTypeMapper.selectById(hardwareModel.getHardwareTypeId());
            if (hardwareType != null) {
                hardwareModelDTO.setHardwareTypeName(hardwareType.getName());
            }
            Manufactory manufactory = manufactoryMapper.selectById(hardwareModel.getManufactoryId());
            hardwareModelDTO.setFactoryName(manufactory.getFactoryName());
            return hardwareModelDTO;
        }).collect(Collectors.toList());
        return PageUtil.toPageDTO(hardwareModelIPage, hardwareModelDTOList);
    }

    /**
     * 2、新增硬件型号
     */
    @Transactional(rollbackFor = Exception.class)
    public BaseResponseEnum add(HardwareModelAddVO hardwareModelAddVO) {
        String username = JsfLoginUtil.getUsername();
        checkUniqueName(hardwareModelAddVO.getName());
        checkUniqueModel(hardwareModelAddVO.getModel());
        HardwareModel hardwareModel = new HardwareModel();
        hardwareModel.setEnable(hardwareModelAddVO.getEnable());
        hardwareModel.setName(hardwareModelAddVO.getName());
        hardwareModel.setModel(hardwareModelAddVO.getModel());
        hardwareModel.setHardwareTypeId(hardwareModelAddVO.getHardwareTypeId());
        hardwareModel.setManufactoryId(hardwareModelAddVO.getManufactoryId());
        hardwareModel.setCreateUser(username);
        hardwareModel.setModifyUser(username);
        int success = hardwareModelMapper.insert(hardwareModel);
        if (success <= 0) {
            log.error("{},期望返回1,实际0!{}", BaseResponseEnum.ERROR_HARDWARE_MODEL_ADD.getMessage(), hardwareModel);
            return BaseResponseEnum.ERROR_HARDWARE_MODEL_ADD;
        }
        for (Integer usageId : hardwareModelAddVO.getHardwareModelUsageIdList()) {
            HardwareModelUsage hardwareModelUsage = new HardwareModelUsage();
            hardwareModelUsage.setHardwareModelId(hardwareModel.getId());
            hardwareModelUsage.setHardwareTypeId(hardwareModelAddVO.getHardwareTypeId());
            hardwareModelUsage.setHardwareTypeUsageId(usageId);
            hardwareModelUsage.setEnable(EnableEnum.ENABLE.getEnable());
            hardwareModelUsage.setCreateUser(username);
            hardwareModelUsage.setModifyUser(username);
            int hardwareModelUsageSuccess = hardwareModelUsageMapper.insert(hardwareModelUsage);
            if (hardwareModelUsageSuccess != 1) {
                throw new AppRuntimeException(
                        String.format("%s,期望返回1,实际%d!", BaseResponseEnum.ERROR_HARDWARE_MODEL_USAGE_ADD.getMessage(), hardwareModelUsageSuccess));
            }
        }
        //保存硬件型号参考资料
        if (hardwareModelAddVO.getReferenceList() != null) {
            List<ReferenceVO> referenceVOS = hardwareModelAddVO.getReferenceList();
            for (ReferenceVO referenceVO : referenceVOS) {
                Reference reference = new Reference();
                reference.setName(referenceVO.getName());
                reference.setAddress(referenceVO.getAddress());
                reference.setHardwareModelId(hardwareModel.getId());
                reference.setCreateUser(username);
                reference.setModifyUser(username);
                referenceMapper.insert(reference);
            }
        }
        return BaseResponseEnum.SUCCESS;
    }

    /**
     * 3、获取硬件型号详情
     */
    public HardwareModelDTO getById(Integer id) {
        HardwareModel hardwareModel = hardwareModelMapper.selectById(id);
        return convertPoToDto(hardwareModel);
    }

    /**
     * 4、编辑硬件型号
     */
    @Transactional(rollbackFor = Exception.class)
    public BaseResponseEnum update(HardwareModelVO hardwareModelVO) {
        String username = JsfLoginUtil.getUsername();
        LambdaQueryWrapper<HardwareModel> hardwareModelNameLambdaQueryWrapper = new LambdaQueryWrapper<>();
        hardwareModelNameLambdaQueryWrapper.eq(HardwareModel::getName, hardwareModelVO.getName());
        List<HardwareModel> hardwareModelNameList = hardwareModelMapper.selectList(hardwareModelNameLambdaQueryWrapper);
        if (!hardwareModelNameList.isEmpty() && !hardwareModelNameList.get(0).getId().equals(hardwareModelVO.getId())) {
            throw new AppRuntimeException(BaseResponseEnum.ERROR_HARDWARE_MODEL_EDIT_NAME_REPEAT.getMessage());
        }
        LambdaQueryWrapper<HardwareModel> hardwareModelModelLambdaQueryWrapper = new LambdaQueryWrapper<>();
        hardwareModelModelLambdaQueryWrapper.eq(HardwareModel::getModel, hardwareModelVO.getModel());
        List<HardwareModel> hardwareModelModelList = hardwareModelMapper.selectList(hardwareModelModelLambdaQueryWrapper);
        if (!hardwareModelModelList.isEmpty() && !hardwareModelModelList.get(0).getId().equals(hardwareModelVO.getId())) {
            throw new AppRuntimeException(BaseResponseEnum.ERROR_HARDWARE_MODEL_EDIT_MODEL_REPEAT.getMessage());
        }
        HardwareModel hardwareModel = new HardwareModel();
        hardwareModel.setEnable(hardwareModelVO.getEnable());
        hardwareModel.setName(hardwareModelVO.getName());
        hardwareModel.setModel(hardwareModelVO.getModel());
        hardwareModel.setHardwareTypeId(hardwareModelVO.getHardwareTypeId());
        hardwareModel.setManufactoryId(hardwareModelVO.getManufactoryId());
        hardwareModel.setId(hardwareModelVO.getId());
        hardwareModel.setModifyUser(username);
        hardwareModel.setModifyTime(new Date());
        int success = hardwareModelMapper.updateById(hardwareModel);
        if (success <= 0) {
            log.error("{},期望返回1,实际0!{}", BaseResponseEnum.ERROR_HARDWARE_MODEL_EDIT.getMessage(), hardwareModel);
            return BaseResponseEnum.ERROR_HARDWARE_MODEL_EDIT;
        }
        //更新硬件型号参考资料
        LambdaQueryWrapper<Reference> referenceLambdaQueryWrapper = new LambdaQueryWrapper<>();
        referenceLambdaQueryWrapper.eq(Reference::getHardwareModelId, hardwareModelVO.getId());
        referenceMapper.delete(referenceLambdaQueryWrapper);
        if (hardwareModelVO.getReferenceList() != null) {
            List<ReferenceVO> referenceVOS = hardwareModelVO.getReferenceList();
            for (ReferenceVO referenceVO : referenceVOS) {
                Reference reference = new Reference();
                reference.setName(referenceVO.getName());
                reference.setAddress(referenceVO.getAddress());
                reference.setHardwareModelId(hardwareModel.getId());
                reference.setCreateUser(username);
                reference.setModifyUser(username);
                referenceMapper.insert(reference);
            }
        }
        updateHardwareTypeUsage(hardwareModelVO);
        return BaseResponseEnum.SUCCESS;
    }

    /**
     * 5、启用停用硬件型号
     */
    @Transactional(rollbackFor = Exception.class)
    public BaseResponseEnum updateEnable(UpdateEnableVO updateEnableVO) {
        HardwareModel hardwareModel = new HardwareModel();
        hardwareModel.setId(updateEnableVO.getId());
        hardwareModel.setEnable(updateEnableVO.getEnable());
        hardwareModel.setModifyUser(JsfLoginUtil.getUsername());
        int success = hardwareModelMapper.updateById(hardwareModel);
        if (success <= 0) {
            log.error("{},期望返回1,实际0!{}", BaseResponseEnum.ERROR_HARDWARE_MODEL_UPDATE_ENABLE.getMessage(), hardwareModel);
            return BaseResponseEnum.ERROR_HARDWARE_MODEL_UPDATE_ENABLE;
        }
        return BaseResponseEnum.SUCCESS;
    }

    /**
     * 6、查询硬件型号数据下拉列表
     */
    public List<CommonVO<Integer>> getSelectList(Integer hardwareTypeUsageId) {
        LambdaQueryWrapper<HardwareModelUsage> hardwareModelUsageLambdaQueryWrapper = new LambdaQueryWrapper<>();
        hardwareModelUsageLambdaQueryWrapper.eq(HardwareModelUsage::getHardwareTypeUsageId, hardwareTypeUsageId);
        List<HardwareModelUsage> hardwareTypeUsageList = hardwareModelUsageMapper.selectList(hardwareModelUsageLambdaQueryWrapper);
        List<CommonVO<Integer>> commonVOList = new ArrayList<>();
        for (HardwareModelUsage hardwareModelUsage : hardwareTypeUsageList) {
            CommonVO<Integer> commonVO = new CommonVO<>();
            commonVO.setEnable(hardwareModelUsage.getEnable());
            HardwareModel hardwareModel = hardwareModelMapper.selectById(hardwareModelUsage.getHardwareModelId());
            if (EnableEnum.DISABLE.getEnable().equals(hardwareModel.getEnable())) {
                commonVO.setEnable(EnableEnum.DISABLE.getEnable());
            }
            if (EnableEnum.ENABLE.getEnable().equals(commonVO.getEnable())) {
                HardwareType hardwareType = hardwareTypeMapper.selectById(hardwareModelUsage.getHardwareTypeId());
                if (EnableEnum.DISABLE.getEnable().equals(hardwareType.getEnable())) {
                    commonVO.setEnable(EnableEnum.DISABLE.getEnable());
                }
            }
            commonVO.setCode(hardwareModelUsage.getHardwareModelId());
            commonVO.setName(hardwareModel.getName());
            commonVO.setParent(hardwareModel.getModel());
            if (EnableEnum.ENABLE.getEnable().equals(commonVO.getEnable())) {
                commonVOList.add(commonVO);
            }
        }
        return commonVOList;
    }

    /**
     * 更新硬件型号用途.
     *
     * @param hardwareModelVO 硬件型号VO.
     * @throws AppRuntimeException 运营系统异常.
     */
    private void updateHardwareTypeUsage(HardwareModelVO hardwareModelVO) {
        String username = JsfLoginUtil.getUsername();
        if (CollectionUtils.isEmpty(hardwareModelVO.getHardwareModelUsageIdList())) {
            return;
        }
        LambdaQueryWrapper<HardwareModelUsage> hardwareModelUsageLambdaQueryWrapper = new LambdaQueryWrapper<>();
        hardwareModelUsageLambdaQueryWrapper.eq(HardwareModelUsage::getHardwareModelId, hardwareModelVO.getId());
        hardwareModelUsageLambdaQueryWrapper.eq(HardwareModelUsage::getEnable, EnableEnum.ENABLE.getEnable());
        List<HardwareModelUsage> hardwareModelUsageListDb = hardwareModelUsageMapper.selectList(hardwareModelUsageLambdaQueryWrapper);
        for (HardwareModelUsage hardwareModelUsageDb : hardwareModelUsageListDb) {
            if (hardwareModelVO.getHardwareModelUsageIdList().contains(hardwareModelUsageDb.getHardwareTypeUsageId())) {
                // 页面传入参数用途包含数据库用途,返回不处理
                continue;
            }
            // 页面传入参数用途不包含的数据库用途,通过enable删除处理
            HardwareModelUsage hardwareModelUsage = new HardwareModelUsage();
            hardwareModelUsage.setId(hardwareModelUsageDb.getId());
            hardwareModelUsage.setEnable(EnableEnum.DISABLE.getEnable());
            hardwareModelUsageMapper.updateById(hardwareModelUsage);
        }
        List<Integer> hardwareModelUsageIdListDb = hardwareModelUsageListDb.stream().map(HardwareModelUsage::getHardwareTypeUsageId).collect(Collectors.toList());
        for (Integer usageId : hardwareModelVO.getHardwareModelUsageIdList()) {
            if (hardwareModelUsageIdListDb.contains(usageId)) {
                // 数据库用途包含页面传入参数用途,返回不处理
                continue;
            }
            // 数据库用途不包含的页面传入参数用途,通过新增添加处理
            HardwareModelUsage hardwareModelUsage = new HardwareModelUsage();
            hardwareModelUsage.setHardwareModelId(hardwareModelVO.getId());
            hardwareModelUsage.setHardwareTypeId(hardwareModelVO.getHardwareTypeId());
            hardwareModelUsage.setHardwareTypeUsageId(usageId);
            hardwareModelUsage.setEnable(EnableEnum.ENABLE.getEnable());
            hardwareModelUsage.setCreateUser(username);
            hardwareModelUsage.setModifyUser(username);
            hardwareModelUsageMapper.insert(hardwareModelUsage);
        }
    }

    /**
     * 校验硬件名称是否唯一.
     *
     * @param name 硬件名称.
     * @throws AppRuntimeException 运营系统异常.
     */
    private void checkUniqueName(String name) {
        LambdaQueryWrapper<HardwareModel> hardwareModelLambdaQueryWrapper = new LambdaQueryWrapper<>();
        hardwareModelLambdaQueryWrapper.eq(HardwareModel::getName, name);
        List<HardwareModel> hardwareModelList = hardwareModelMapper.selectList(hardwareModelLambdaQueryWrapper);
        if (!CollectionUtils.isEmpty(hardwareModelList)) {
            throw new AppRuntimeException(BaseResponseEnum.ERROR_HARDWARE_MODEL_ADD_NAME_REPEAT.getMessage());
        }
    }

    /**
     * 校验硬件型号名称是否唯一.
     *
     * @param model 硬件型号.
     * @throws AppRuntimeException 运营系统异常.
     */
    private void checkUniqueModel(String model) {
        LambdaQueryWrapper<HardwareModel> hardwareModelLambdaQueryWrapper = new LambdaQueryWrapper<>();
        hardwareModelLambdaQueryWrapper.eq(HardwareModel::getModel, model);
        List<HardwareModel> hardwareModelList = hardwareModelMapper.selectList(hardwareModelLambdaQueryWrapper);
        if (!CollectionUtils.isEmpty(hardwareModelList)) {
            throw new AppRuntimeException(BaseResponseEnum.ERROR_HARDWARE_MODEL_ADD_MODEL_REPEAT.getMessage());
        }
    }

    /**
     * 硬件型号PO转换为DTO.
     *
     * @param hardwareModel 硬件型号.
     */
    private HardwareModelDTO convertPoToDto(HardwareModel hardwareModel) {
        if (hardwareModel == null) {
            return null;
        }
        HardwareModelDTO hardwareModelDTO = new HardwareModelDTO();
        hardwareModelDTO.setName(hardwareModel.getName());
        hardwareModelDTO.setModel(hardwareModel.getModel());
        hardwareModelDTO.setHardwareTypeId(hardwareModel.getHardwareTypeId());
        hardwareModelDTO.setManufactoryId(hardwareModel.getManufactoryId());
        hardwareModelDTO.setCreateTime(hardwareModel.getCreateTime());
        hardwareModelDTO.setModifyTime(hardwareModel.getModifyTime());
        hardwareModelDTO.setModifyUser(hardwareModel.getModifyUser());
        hardwareModelDTO.setEnable(hardwareModel.getEnable());
        hardwareModelDTO.setEnableName(EnableEnum.getNameByEnable(hardwareModel.getEnable()));
        hardwareModelDTO.setId(hardwareModel.getId());
        //补充硬件型号参考资料信息
        LambdaQueryWrapper<Reference> referenceLambdaQueryWrapper = new LambdaQueryWrapper<>();
        referenceLambdaQueryWrapper.eq(Reference::getHardwareModelId, hardwareModel.getId());
        List<Reference> referenceList = referenceMapper.selectList(referenceLambdaQueryWrapper);
        hardwareModelDTO.setReferenceList(referenceList);
        hardwareModelDTO.setHardwareModelUsageIdList(getUsageIdListById(hardwareModel.getId()));
        return hardwareModelDTO;
    }

    /**
     * 通过硬件型号ID获取应用用途列表.
     *
     * @param hardwareModelId 硬件型号ID.
     */
    private List<Integer> getUsageIdListById(Integer hardwareModelId) {
        LambdaQueryWrapper<HardwareModelUsage> hardwareModelUsageLambdaQueryWrapper = new LambdaQueryWrapper<>();
        hardwareModelUsageLambdaQueryWrapper.eq(HardwareModelUsage::getHardwareModelId, hardwareModelId);
        hardwareModelUsageLambdaQueryWrapper.eq(HardwareModelUsage::getEnable, EnableEnum.ENABLE.getEnable());
        List<HardwareModelUsage> hardwareModelUsageList = hardwareModelUsageMapper.selectList(hardwareModelUsageLambdaQueryWrapper);
        return hardwareModelUsageList.stream().map(HardwareModelUsage::getHardwareTypeUsageId).collect(Collectors.toList());
    }
}

