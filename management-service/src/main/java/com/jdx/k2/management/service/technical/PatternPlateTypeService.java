/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.k2.management.service.technical;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jdx.k2.management.common.utils.PageUtil;
import com.jdx.k2.management.domain.dto.technical.PatternPlateTypeDTO;
import com.jdx.k2.management.domain.po.technical.PatternPlateType;
import com.jdx.k2.management.domain.vo.technical.PatternPlateTypeAddVO;
import com.jdx.k2.management.repository.mapper.technical.PatternPlateTypeMapper;
import com.jdx.rover.common.domain.page.PageDTO;
import com.jdx.rover.common.domain.page.PageVO;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

/**
 * 影子系统-模板指令管理-service
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class PatternPlateTypeService {

    @Resource
    private PatternPlateTypeMapper patternPlateTypeMapper;

    /**
     * 1、分页查询模板列表
     */
    public PageDTO<PatternPlateTypeDTO> getPatternPlateTypePageList(PageVO pageVO) {
        IPage<PatternPlateType> paramPage = new Page<>(pageVO.getPageNum(), pageVO.getPageSize());
        LambdaQueryWrapper<PatternPlateType> queryWrapper = new LambdaQueryWrapper<>();
        IPage<PatternPlateType> resultPage = patternPlateTypeMapper.selectPage(paramPage, queryWrapper);
        List<PatternPlateTypeDTO> patternPlateTypeResultList = new ArrayList<>();
        resultPage.getRecords().forEach(type -> {
            PatternPlateTypeDTO patternPlateTypeDTO = new PatternPlateTypeDTO();
            patternPlateTypeDTO.setId(type.getId());
            patternPlateTypeDTO.setEventType(type.getEventType());
            patternPlateTypeDTO.setEventTypeName(type.getEventTypeName());
            patternPlateTypeResultList.add(patternPlateTypeDTO);
        });

        return PageUtil.toPageDTO(resultPage, patternPlateTypeResultList);
    }

    /**
     * 2、新增模板类型
     */
    @Transactional(rollbackFor = Exception.class)
    public Integer addPatternPlateType(PatternPlateTypeAddVO patternPlateTypeAddVO) {
        PatternPlateType patternPlateType = new PatternPlateType();
        patternPlateType.setEventType(patternPlateTypeAddVO.getType());
        patternPlateType.setEventTypeName(patternPlateTypeAddVO.getTypeName());
        patternPlateTypeMapper.insert(patternPlateType);
        return patternPlateType.getId();
    }

    /**
     * 3、查询模板类型列表
     */
    public List<PatternPlateTypeDTO> getPatternPlateTypeList() {
        LambdaQueryWrapper<PatternPlateType> queryWrapper = new LambdaQueryWrapper<>();
        List<PatternPlateType> patternPlateTypeList = patternPlateTypeMapper.selectList(queryWrapper);
        List<PatternPlateTypeDTO> resultDTOList = new ArrayList<>();
        if (CollectionUtils.isEmpty(patternPlateTypeList)) {
            return resultDTOList;
        }
        return patternPlateTypeList.stream().map(type -> {
            PatternPlateTypeDTO patternPlateTypeDTO = new PatternPlateTypeDTO();
            patternPlateTypeDTO.setId(type.getId());
            patternPlateTypeDTO.setEventType(type.getEventType());
            patternPlateTypeDTO.setEventTypeName(type.getEventTypeName());
            return patternPlateTypeDTO;
        }).collect(Collectors.toList());
    }

}
