package com.jdx.k2.management.service.deploy;

import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.type.TypeReference;
import com.jdx.k2.management.common.constants.Constants;
import com.jdx.k2.management.common.enums.DeviceInsuranceStatusEnum;
import com.jdx.k2.management.common.enums.InsuranceStatusEnum;
import com.jdx.k2.management.common.enums.base.BaseResponseEnum;
import com.jdx.k2.management.common.exception.AppRuntimeException;
import com.jdx.k2.management.common.utils.PageUtil;
import com.jdx.k2.management.domain.bo.deploy.DeviceInsuranceBO;
import com.jdx.k2.management.domain.dto.common.AttachmentDTO;
import com.jdx.k2.management.domain.dto.deploy.*;
import com.jdx.k2.management.domain.handler.AttachmentListTypeHandler;
import com.jdx.k2.management.domain.handler.json.Attachment;
import com.jdx.k2.management.domain.po.deploy.InsuranceInfo;
import com.jdx.k2.management.domain.po.deploy.DeviceInsuranceInfo;
import com.jdx.k2.management.domain.vo.deploy.*;
import com.jdx.k2.management.manager.middleware.S3Manager;
import com.jdx.k2.management.repository.mapper.deploy.InsuranceInfoMapper;
import com.jdx.k2.management.repository.mapper.deploy.DeviceInsuranceInfoMapper;
import com.jdx.rover.common.domain.page.PageDTO;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.jsf.util.JsfLoginUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.jdx.k2.management.common.constants.Constants.DELETED;
import static com.jdx.k2.management.common.constants.Constants.SYSTEM;

/**
 * 保险相关service
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class InsuranceService {

    private final InsuranceInfoMapper insuranceInfoMapper;

    private final DeviceInsuranceInfoMapper deviceInsuranceInfoMapper;

    private final S3Manager s3Manager;


    /**
     * @param :
     * @return void
     * @description 定时任务更新保险状态
     * <AUTHOR>
     * @date 2025/3/5 10:55
     */
    public void updateInsuranceStatus() {
        log.info("开始执行更新保险定时任务");
        LambdaQueryWrapper<InsuranceInfo> insuranceInfoLambdaQueryWrapper = new LambdaQueryWrapper<>();
        List<String> insuranceStatusList = Arrays.asList(InsuranceStatusEnum.PENDING.getValue(), InsuranceStatusEnum.EFFECTIVE.getValue());
        insuranceInfoLambdaQueryWrapper.in(InsuranceInfo::getInsuredStatus, insuranceStatusList);
        List<InsuranceInfo> insuranceInfoList = insuranceInfoMapper.selectList(insuranceInfoLambdaQueryWrapper);
        DateTime startOfDay = DateUtil.beginOfDay(DateUtil.date());
        for (InsuranceInfo insuranceInfo : insuranceInfoList) {
            if (InsuranceStatusEnum.PENDING.getValue().equals(insuranceInfo.getInsuredStatus()) && startOfDay.isAfterOrEquals(insuranceInfo.getStartTime())) {
                log.info("更新保险状态为生效,id:{}", insuranceInfo.getId());
                //将待生效的任务改为生效
                LambdaUpdateWrapper<InsuranceInfo> insuranceInfoLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
                insuranceInfoLambdaUpdateWrapper.eq(InsuranceInfo::getId, insuranceInfo.getId());
                insuranceInfoLambdaUpdateWrapper.set(InsuranceInfo::getInsuredStatus, InsuranceStatusEnum.EFFECTIVE.getValue());
                insuranceInfoMapper.update(null, insuranceInfoLambdaUpdateWrapper);
                //修改车辆投保表 保单状态(方便后续展示用)
                LambdaUpdateWrapper<DeviceInsuranceInfo> deviceInsuranceInfoLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
                deviceInsuranceInfoLambdaUpdateWrapper.eq(DeviceInsuranceInfo::getInsuranceInfoId, insuranceInfo.getId());
                deviceInsuranceInfoLambdaUpdateWrapper.set(DeviceInsuranceInfo::getInsuredStatus, InsuranceStatusEnum.EFFECTIVE.getValue());
                deviceInsuranceInfoMapper.update(null, deviceInsuranceInfoLambdaUpdateWrapper);
            } else if (InsuranceStatusEnum.EFFECTIVE.getValue().equals(insuranceInfo.getInsuredStatus()) && startOfDay.isAfter(insuranceInfo.getEndTime())) {
                log.info("更新保险状态为失效,id:{}", insuranceInfo.getId());
                //将生效任务改为失效
                LambdaUpdateWrapper<InsuranceInfo> insuranceInfoLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
                insuranceInfoLambdaUpdateWrapper.eq(InsuranceInfo::getId, insuranceInfo.getId());
                insuranceInfoLambdaUpdateWrapper.set(InsuranceInfo::getInsuredStatus, InsuranceStatusEnum.INACTIVE.getValue());
                insuranceInfoMapper.update(null, insuranceInfoLambdaUpdateWrapper);
                //修改车辆投保状态为失效
                LambdaUpdateWrapper<DeviceInsuranceInfo> deviceInsuranceInfoLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
                deviceInsuranceInfoLambdaUpdateWrapper.eq(DeviceInsuranceInfo::getInsuranceInfoId, insuranceInfo.getId());
                deviceInsuranceInfoLambdaUpdateWrapper.ne(DeviceInsuranceInfo::getDeviceInsuredStatus, DeviceInsuranceStatusEnum.LAPSED.getValue());
                deviceInsuranceInfoLambdaUpdateWrapper.set(DeviceInsuranceInfo::getDeviceInsuredStatus, DeviceInsuranceStatusEnum.INACTIVE.getValue());
                deviceInsuranceInfoMapper.update(null, deviceInsuranceInfoLambdaUpdateWrapper);
                //修改车辆投保表 保单状态(方便后续展示用)
                deviceInsuranceInfoLambdaUpdateWrapper.clear();
                deviceInsuranceInfoLambdaUpdateWrapper.eq(DeviceInsuranceInfo::getInsuranceInfoId, insuranceInfo.getId());
                deviceInsuranceInfoLambdaUpdateWrapper.set(DeviceInsuranceInfo::getInsuredStatus, InsuranceStatusEnum.INACTIVE.getValue());
                deviceInsuranceInfoMapper.update(null, deviceInsuranceInfoLambdaUpdateWrapper);
            }
        }
        log.info("保险定时任务执行完成");
    }

    /**
     * @param insurancePageVO:
     * @return PageDTO<InsurancePageDTO>
     * @description 分页获取保险模块接口
     * <AUTHOR>
     * @date 2025/3/5 10:53
     */
    public PageDTO<InsurancePageDTO> getPageList(InsurancePageVO insurancePageVO) {
        IPage<InsuranceInfo> iPage = new Page<>(insurancePageVO.getPageNum(), insurancePageVO.getPageSize());
        QueryWrapper<InsuranceInfo> insuranceInfoQueryWrapper = new QueryWrapper<>();
        insuranceInfoQueryWrapper.like(StringUtils.isNotBlank(insurancePageVO.getPolicyNumber()), "ii.policy_number", insurancePageVO.getPolicyNumber());
        insuranceInfoQueryWrapper.like(StringUtils.isNotBlank(insurancePageVO.getDeviceName()), "dii.device_name", insurancePageVO.getDeviceName());
        insuranceInfoQueryWrapper.eq(StringUtils.isNotBlank(insurancePageVO.getInsuredStatus()), "ii.insured_status", insurancePageVO.getInsuredStatus());
        insuranceInfoQueryWrapper.between(Objects.nonNull(insurancePageVO.getEffectiveStartTime()) && Objects.nonNull(insurancePageVO.getEffectiveEndTime()),
                "ii.start_time", insurancePageVO.getEffectiveStartTime(), insurancePageVO.getEffectiveEndTime());
        insuranceInfoQueryWrapper.eq("ii.deleted", DELETED);
        insuranceInfoQueryWrapper.orderByDesc("ii.create_time");
        IPage<InsuranceInfo> insuranceInfoIPage = insuranceInfoMapper.selectPageByParams(iPage, insuranceInfoQueryWrapper);

        //转换并返回
        List<InsurancePageDTO> insurancePageDTOList = insuranceInfoIPage.getRecords().stream().map(this::convertInsuranceInfoToPageDTO).collect(Collectors.toList());
        return PageUtil.toPageDTO(insuranceInfoIPage, insurancePageDTOList);
    }

    /**
     * @param deviceInsurancePageVO:
     * @return void
     * @description 分页获取投保车辆明细
     * <AUTHOR>
     * @date 2025/3/5 10:54
     */
    public PageDTO<DeviceInsurancePageDTO> getDeviceInsurancePageList(DeviceInsurancePageVO deviceInsurancePageVO) {
        IPage<DeviceInsuranceInfo> iPage = new Page<>(deviceInsurancePageVO.getPageNum(), deviceInsurancePageVO.getPageSize());
        LambdaQueryWrapper<DeviceInsuranceInfo> deviceInsuranceInfoLambdaQueryWrapper = new LambdaQueryWrapper<>();
        deviceInsuranceInfoLambdaQueryWrapper.eq(DeviceInsuranceInfo::getInsuranceInfoId, deviceInsurancePageVO.getInsuranceId());
        deviceInsuranceInfoLambdaQueryWrapper.orderBy(true, false, DeviceInsuranceInfo::getDeviceInsuredStatus, DeviceInsuranceInfo::getDeviceName);
        IPage<DeviceInsuranceInfo> deviceInsuranceInfoIPage = deviceInsuranceInfoMapper.selectPage(iPage, deviceInsuranceInfoLambdaQueryWrapper);

        //转换并返回
        List<DeviceInsurancePageDTO> insurancePageDTOList = deviceInsuranceInfoIPage.getRecords().stream().map(this::convertDeviceInsuranceInfoToPageDTO).collect(Collectors.toList());
        return PageUtil.toPageDTO(deviceInsuranceInfoIPage, insurancePageDTOList);
    }

    /**
     * @param insuranceAddVO:
     * @return void
     * @description 新建车辆保险
     * <AUTHOR>
     * @date 2025/3/5 10:54
     */
    @Transactional(rollbackFor = Exception.class)
    public void addInsurance(InsuranceAddVO insuranceAddVO) {
        String username = JsfLoginUtil.getUsernameUseDefault(SYSTEM);

        //构造保单信息
        InsuranceInfo insuranceInfo = new InsuranceInfo();
        insuranceInfo.setPolicyNumber(insuranceAddVO.getPolicyNumber());
        insuranceInfo.setInsuranceCompany(insuranceAddVO.getInsuranceCompany());
        insuranceInfo.setInsuranceType(insuranceAddVO.getInsuranceType());
        insuranceInfo.setInsuredEntity(insuranceAddVO.getInsuredEntity());
        insuranceInfo.setPolicyCoverageAmount(insuranceAddVO.getPolicyCoverageAmount());

        DateTime startDate = DateUtil.parse(insuranceAddVO.getEffectiveStartTime(), "yyyy-MM-dd");
        DateTime endDate = DateUtil.parse(insuranceAddVO.getEffectiveEndTime(), "yyyy-MM-dd");
        insuranceInfo.setStartTime(startDate);
        insuranceInfo.setEndTime(endDate);
        //判断保单状态
        DateTime startOfDay = DateUtil.beginOfDay(DateUtil.date());
        if (startOfDay.before(startDate)) {
            //生效开始时间大于当前时间为待生效
            insuranceInfo.setInsuredStatus(InsuranceStatusEnum.PENDING.getValue());
        } else if (startOfDay.after(endDate)) {
            //生效结束时间小于当前时间为已失效
            insuranceInfo.setInsuredStatus(InsuranceStatusEnum.INACTIVE.getValue());
        } else {
            //否则为生效中
            insuranceInfo.setInsuredStatus(InsuranceStatusEnum.EFFECTIVE.getValue());
        }
        //保单列表
        List<Attachment> attachmentList = new ArrayList<>();
        Attachment attachment = new Attachment(insuranceAddVO.getPolicyAttachment());
        attachmentList.add(attachment);
        insuranceInfo.setPolicyAttachment(attachmentList);
        //其他附件列表
        if (!CollectionUtils.isEmpty(insuranceAddVO.getOtherAttachmentList())) {
            List<Attachment> otherAttachmentList = insuranceAddVO.getOtherAttachmentList().stream().map(Attachment::new).collect(Collectors.toList());
            insuranceInfo.setOtherAttachment(otherAttachmentList);
        }
        insuranceInfo.setInsuredDeviceCount(insuranceAddVO.getDeviceInsuranceInfoList().size());
        insuranceInfo.setRemark(insuranceAddVO.getRemark());
        insuranceInfo.setCreateUser(username);
        insuranceInfo.setModifyUser(username);
        //插入保单信息
        insuranceInfoMapper.insert(insuranceInfo);

        //构造投保车辆信息
        for (InsuranceDeviceAddVO insuranceDeviceAddVO : insuranceAddVO.getDeviceInsuranceInfoList()) {
            DeviceInsuranceInfo deviceInsuranceInfo = new DeviceInsuranceInfo();
            deviceInsuranceInfo.setInsuranceInfoId(insuranceInfo.getId());
            deviceInsuranceInfo.setPolicyNumber(insuranceInfo.getPolicyNumber());
            deviceInsuranceInfo.setDeviceName(insuranceDeviceAddVO.getDeviceName());
            deviceInsuranceInfo.setSerialNo(insuranceDeviceAddVO.getSerialNo());
            deviceInsuranceInfo.setStationName(insuranceDeviceAddVO.getStationName());
            deviceInsuranceInfo.setCityName(insuranceDeviceAddVO.getCityName());
            deviceInsuranceInfo.setProvinceName(insuranceDeviceAddVO.getProvinceName());
            deviceInsuranceInfo.setRadius(insuranceDeviceAddVO.getRadius());
            //判断设备投保状态:如果是待生效或已生效就置为正常,如果是失效的则置为失效
            deviceInsuranceInfo.setDeviceInsuredStatus(InsuranceStatusEnum.INACTIVE.getValue().equals(insuranceInfo.getInsuredStatus()) ? DeviceInsuranceStatusEnum.INACTIVE.getValue() : DeviceInsuranceStatusEnum.NORMAL.getValue());
            deviceInsuranceInfo.setInsuredStatus(insuranceInfo.getInsuredStatus());
            deviceInsuranceInfo.setRemark(insuranceDeviceAddVO.getRemark());
            deviceInsuranceInfo.setCreateUser(username);
            deviceInsuranceInfo.setModifyUser(username);
            //插入设备保险信息
            deviceInsuranceInfoMapper.insert(deviceInsuranceInfo);
        }
    }

    /**
     * @param insuranceDetailVO:
     * @return void
     * @description 获取车辆保险详情
     * <AUTHOR>
     * @date 2025/3/5 10:55
     */
    public InsuranceDetailDTO getInsuranceDetail(InsuranceDetailVO insuranceDetailVO) {
        //查询保单
        InsuranceInfo insuranceInfo = insuranceInfoMapper.selectById(insuranceDetailVO.getInsuranceId());
        if (Objects.isNull(insuranceInfo)) {
            return null;
        }
        InsuranceDetailDTO insuranceDetailDTO = new InsuranceDetailDTO();
        insuranceDetailDTO.setInsuranceId(insuranceInfo.getId());
        insuranceDetailDTO.setPolicyNumber(insuranceInfo.getPolicyNumber());
        insuranceDetailDTO.setInsuranceCompany(insuranceInfo.getInsuranceCompany());
        insuranceDetailDTO.setInsuranceType(insuranceInfo.getInsuranceType());
        insuranceDetailDTO.setInsuredEntity(insuranceInfo.getInsuredEntity());
        insuranceDetailDTO.setPolicyCoverageAmount(insuranceInfo.getPolicyCoverageAmount());
        insuranceDetailDTO.setEffectiveStartTime(insuranceInfo.getStartTime());
        insuranceDetailDTO.setEffectiveEndTime(insuranceInfo.getEndTime());
        insuranceDetailDTO.setRemark(insuranceInfo.getRemark());
        //保单信息
        if (!CollectionUtils.isEmpty(insuranceInfo.getPolicyAttachment())) {
            Attachment attachment = insuranceInfo.getPolicyAttachment().get(0);
            AttachmentDTO attachmentDTO = new AttachmentDTO(attachment);
            //生成外链
            String url = s3Manager.generateUrl(attachment.getBucketName(), attachment.getFileKey(), Constants.EXPIRE_TIME_MS);
            attachmentDTO.setUrl(url);
            insuranceDetailDTO.setPolicyAttachment(attachmentDTO);
        }
        //其他附件
        if (!CollectionUtils.isEmpty(insuranceInfo.getOtherAttachment())) {
            List<AttachmentDTO> otherAttachmentList = new ArrayList<>();
            insuranceInfo.getOtherAttachment().forEach(attachment -> {
                AttachmentDTO attachmentDTO = new AttachmentDTO(attachment);
                //生成外链
                String url = s3Manager.generateUrl(attachment.getBucketName(), attachment.getFileKey(), Constants.EXPIRE_TIME_MS);
                attachmentDTO.setUrl(url);
                otherAttachmentList.add(attachmentDTO);
            });
            insuranceDetailDTO.setOtherAttachmentList(otherAttachmentList);
        }
        //投保设备信息
        LambdaQueryWrapper<DeviceInsuranceInfo> deviceInsuranceInfoLambdaQueryWrapper = new LambdaQueryWrapper<>();
        deviceInsuranceInfoLambdaQueryWrapper.eq(DeviceInsuranceInfo::getInsuranceInfoId, insuranceInfo.getId());
        List<DeviceInsuranceInfo> deviceInsuranceInfoList = deviceInsuranceInfoMapper.selectList(deviceInsuranceInfoLambdaQueryWrapper);
        if (!CollectionUtils.isEmpty(deviceInsuranceInfoList)) {
            List<InsuranceDeviceDTO> insuranceDeviceDTOList = new ArrayList<>();
            deviceInsuranceInfoList.forEach(deviceInsuranceInfo -> {
                InsuranceDeviceDTO insuranceDeviceDTO = new InsuranceDeviceDTO();
                insuranceDeviceDTO.setDeviceInsuranceId(deviceInsuranceInfo.getId());
                insuranceDeviceDTO.setDeviceName(deviceInsuranceInfo.getDeviceName());
                insuranceDeviceDTO.setSerialNo(deviceInsuranceInfo.getSerialNo());
                insuranceDeviceDTO.setProvinceName(deviceInsuranceInfo.getProvinceName());
                insuranceDeviceDTO.setCityName(deviceInsuranceInfo.getCityName());
                insuranceDeviceDTO.setStationName(deviceInsuranceInfo.getStationName());
                insuranceDeviceDTO.setRadius(deviceInsuranceInfo.getRadius());
                insuranceDeviceDTO.setDeviceInsuredStatus(deviceInsuranceInfo.getDeviceInsuredStatus());
                insuranceDeviceDTO.setRemark(deviceInsuranceInfo.getRemark());
                insuranceDeviceDTOList.add(insuranceDeviceDTO);
            });
            insuranceDeviceDTOList.sort(Comparator.comparing(
                item -> DeviceInsuranceStatusEnum.LAPSED.getValue()
                    .equals(item.getDeviceInsuredStatus())));
            insuranceDetailDTO.setDeviceInsuranceInfoList(insuranceDeviceDTOList);
        }

        return insuranceDetailDTO;
    }

    /**
     * @param deviceInsuranceEditVO:
     * @return void
     * @description 编辑车辆保险
     * <AUTHOR>
     * @date 2025/3/5 10:55
     */
    @Transactional(rollbackFor = Exception.class)
    public void editDeviceInsurance(DeviceInsuranceEditVO deviceInsuranceEditVO) {
        String username = JsfLoginUtil.getUsernameUseDefault(SYSTEM);
        //校验失效的保单不能编辑
        LambdaQueryWrapper<InsuranceInfo> insuranceInfoLambdaQueryWrapper = new LambdaQueryWrapper<>();
        insuranceInfoLambdaQueryWrapper.eq(InsuranceInfo::getId, deviceInsuranceEditVO.getInsuranceId());
        insuranceInfoLambdaQueryWrapper.select(InsuranceInfo::getEndTime);
        InsuranceInfo insuranceInfo = insuranceInfoMapper.selectOne(insuranceInfoLambdaQueryWrapper);
        if (Objects.isNull(insuranceInfo)) {
            throw new AppRuntimeException(BaseResponseEnum.ERROR_INSURANCE_INFO_NOT_EXISTS);
        }
        DateTime startOfDay = DateUtil.beginOfDay(DateUtil.date());
        if (insuranceInfo.getEndTime().getTime() < startOfDay.getTime()) {
            throw new AppRuntimeException(BaseResponseEnum.ERROR_INSURANCE_EDIT_INACTIVE);
        }
        //更新保单信息
        LambdaUpdateWrapper<InsuranceInfo> insuranceInfoLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        insuranceInfoLambdaUpdateWrapper.eq(InsuranceInfo::getId, deviceInsuranceEditVO.getInsuranceId());
        insuranceInfoLambdaUpdateWrapper.set(InsuranceInfo::getPolicyNumber, deviceInsuranceEditVO.getPolicyNumber());
        insuranceInfoLambdaUpdateWrapper.set(InsuranceInfo::getInsuranceCompany, deviceInsuranceEditVO.getInsuranceCompany());
        insuranceInfoLambdaUpdateWrapper.set(InsuranceInfo::getInsuranceType, deviceInsuranceEditVO.getInsuranceType());
        insuranceInfoLambdaUpdateWrapper.set(InsuranceInfo::getInsuredEntity, deviceInsuranceEditVO.getInsuredEntity());
        insuranceInfoLambdaUpdateWrapper.set(InsuranceInfo::getPolicyCoverageAmount, deviceInsuranceEditVO.getPolicyCoverageAmount());
        insuranceInfoLambdaUpdateWrapper.set(InsuranceInfo::getStartTime, deviceInsuranceEditVO.getEffectiveStartTime());
        insuranceInfoLambdaUpdateWrapper.set(InsuranceInfo::getEndTime, deviceInsuranceEditVO.getEffectiveEndTime());
        //判断保单状态
        DateTime startDate = DateUtil.parse(deviceInsuranceEditVO.getEffectiveStartTime(), "yyyy-MM-dd");
        DateTime endDate = DateUtil.parse(deviceInsuranceEditVO.getEffectiveEndTime(), "yyyy-MM-dd");
        String insuranceStatus = null;
        if (startOfDay.before(startDate)) {
            //生效开始时间大于当前时间为待生效
            insuranceStatus = InsuranceStatusEnum.PENDING.getValue();
        } else if (startOfDay.after(endDate)) {
            //生效结束时间小于当前时间为已失效
            insuranceStatus = InsuranceStatusEnum.INACTIVE.getValue();
        } else {
            //否则为生效中
            insuranceStatus = InsuranceStatusEnum.EFFECTIVE.getValue();
        }
        insuranceInfoLambdaUpdateWrapper.set(InsuranceInfo::getInsuredStatus, insuranceStatus);
        //保单文件
        List<Attachment> attachmentList = new ArrayList<>();
        attachmentList.add(new Attachment(deviceInsuranceEditVO.getPolicyAttachment()));
        insuranceInfoLambdaUpdateWrapper.set(InsuranceInfo::getPolicyAttachment, attachmentList, "typeHandler=" + AttachmentListTypeHandler.class.getName());
        insuranceInfoLambdaUpdateWrapper.set(InsuranceInfo::getOtherAttachment, deviceInsuranceEditVO.getOtherAttachmentList(), "typeHandler=" + AttachmentListTypeHandler.class.getName());
        insuranceInfoLambdaUpdateWrapper.set(InsuranceInfo::getRemark, deviceInsuranceEditVO.getRemark());
        insuranceInfoLambdaUpdateWrapper.set(InsuranceInfo::getModifyUser, username);
        insuranceInfoMapper.update(null, insuranceInfoLambdaUpdateWrapper);

        //更新保单设备信息
        for (InsuranceDeviceEditVO insuranceDeviceEditVO : deviceInsuranceEditVO.getDeviceInsuranceInfoList()) {
            LambdaUpdateWrapper<DeviceInsuranceInfo> deviceInsuranceInfoLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
            //deviceInsuranceInfoLambdaUpdateWrapper.eq(DeviceInsuranceInfo::getInsuranceInfoId, deviceInsuranceEditVO.getInsuranceId());
            //deviceInsuranceInfoLambdaUpdateWrapper.eq(DeviceInsuranceInfo::getDeviceName, insuranceDeviceEditVO.getDeviceName());
            deviceInsuranceInfoLambdaUpdateWrapper.eq(DeviceInsuranceInfo::getId, insuranceDeviceEditVO.getDeviceInsuranceId());
            deviceInsuranceInfoLambdaUpdateWrapper.set(DeviceInsuranceInfo::getRadius, insuranceDeviceEditVO.getRadius());
            deviceInsuranceInfoLambdaUpdateWrapper.set(DeviceInsuranceInfo::getInsuredStatus, insuranceStatus);
            //维护设备的保单状态:如果保单变为失效且设备投保状态是正常就变为失效
            if (InsuranceStatusEnum.INACTIVE.getValue().equals(insuranceStatus) && DeviceInsuranceStatusEnum.NORMAL.getValue().equals(insuranceDeviceEditVO.getDeviceInsuredStatus())) {
                insuranceDeviceEditVO.setDeviceInsuredStatus(DeviceInsuranceStatusEnum.INACTIVE.getValue());
            }
            deviceInsuranceInfoLambdaUpdateWrapper.set(DeviceInsuranceInfo::getDeviceInsuredStatus, insuranceDeviceEditVO.getDeviceInsuredStatus());
            deviceInsuranceInfoLambdaUpdateWrapper.set(DeviceInsuranceInfo::getRemark, insuranceDeviceEditVO.getRemark());
            deviceInsuranceInfoLambdaUpdateWrapper.set(DeviceInsuranceInfo::getModifyUser, username);
            deviceInsuranceInfoMapper.update(null, deviceInsuranceInfoLambdaUpdateWrapper);
        }
    }

    /**
     * @param insuranceInfo:
     * @return InsurancePageDTO
     * @description 将InsuranceInfo转为分页返回的DTO
     * <AUTHOR>
     * @date 2025/3/5 14:32
     */
    private InsurancePageDTO convertInsuranceInfoToPageDTO(InsuranceInfo insuranceInfo) {
        InsurancePageDTO insurancePageDTO = new InsurancePageDTO();
        insurancePageDTO.setInsuranceId(insuranceInfo.getId());
        insurancePageDTO.setPolicyNumber(insuranceInfo.getPolicyNumber());
        insurancePageDTO.setInsuranceCompany(insuranceInfo.getInsuranceCompany());
        insurancePageDTO.setInsuranceType(insuranceInfo.getInsuranceType());
        insurancePageDTO.setInsuredEntity(insuranceInfo.getInsuredEntity());
        insurancePageDTO.setDeviceCount(insuranceInfo.getInsuredDeviceCount());
        insurancePageDTO.setPolicyCoverageAmount(insuranceInfo.getPolicyCoverageAmount());
        insurancePageDTO.setEffectiveStartTime(insuranceInfo.getStartTime());
        insurancePageDTO.setEffectiveEndTime(insuranceInfo.getEndTime());
        insurancePageDTO.setInsuredStatus(insuranceInfo.getInsuredStatus());
        insurancePageDTO.setInsuredStatusName(InsuranceStatusEnum.getNameByValue(insuranceInfo.getInsuredStatus()));
        insurancePageDTO.setModifyUser(insuranceInfo.getModifyUser());
        insurancePageDTO.setModifyTime(insuranceInfo.getModifyTime());
        return insurancePageDTO;
    }

    /**
     * @param deviceInsuranceInfo:
     * @return DeviceInsurancePageDTO
     * @description 将DeviceInsuranceInfo转换为分页返回的DTO
     * <AUTHOR>
     * @date 2025/3/5 15:32
     */
    private DeviceInsurancePageDTO convertDeviceInsuranceInfoToPageDTO(DeviceInsuranceInfo deviceInsuranceInfo) {
        DeviceInsurancePageDTO deviceInsurancePageDTO = new DeviceInsurancePageDTO();
        deviceInsurancePageDTO.setDeviceInsuranceId(deviceInsuranceInfo.getInsuranceInfoId());
        deviceInsurancePageDTO.setDeviceName(deviceInsuranceInfo.getDeviceName());
        deviceInsurancePageDTO.setSerialNo(deviceInsuranceInfo.getSerialNo());
        deviceInsurancePageDTO.setProvinceName(deviceInsuranceInfo.getProvinceName());
        deviceInsurancePageDTO.setCityName(deviceInsuranceInfo.getCityName());
        deviceInsurancePageDTO.setStationName(deviceInsuranceInfo.getStationName());
        deviceInsurancePageDTO.setRadius(deviceInsuranceInfo.getRadius());
        deviceInsurancePageDTO.setDeviceInsuredStatus(deviceInsuranceInfo.getDeviceInsuredStatus());
        deviceInsurancePageDTO.setDeviceInsuredStatusName(DeviceInsuranceStatusEnum.getNameByValue(deviceInsuranceInfo.getDeviceInsuredStatus()));
        return deviceInsurancePageDTO;
    }

    /**
     * 批量拉取车辆生效保单接口
     *
     * @param vo
     * @return
     */
    public List<DeviceInsuranceDTO> getDevicesEffectiveInsuranceList(DeviceListInsuranceVO vo) {
        //获取当前设备类别下全部保单信息
        List<DeviceInsuranceBO> deviceInsurances = deviceInsuranceInfoMapper.selectDeviceEffectiveInsuranceInfoList(vo.getDeviceNameList());
        if (deviceInsurances.isEmpty()) {
            return Collections.emptyList();
        }
        //获取当前生效中的保单，按设备名称分组，即获得该设备下全部已生效保单
        Map<String, List<DeviceInsuranceBO>> boGroup = CollStreamUtil.groupByKey(deviceInsurances, DeviceInsuranceBO::getDeviceName);
        //遍历请求的设备列表，去分组中查询
        return CollStreamUtil.toList(vo.getDeviceNameList(), deviceName -> {
            List<DeviceInsuranceBO> subList = boGroup.get(deviceName);
            return DeviceInsuranceDTO.builder()
                    .deviceName(deviceName)
                    .deviceInsurances(CollectionUtil.isEmpty(subList)
                            ? Collections.emptyList()
                            : CollStreamUtil.toList(subList, this::buildDeviceInsuranceItem))
                    .build();
        });
    }

    /**
     * 构建设备关联的保单项
     */
    private DeviceInsuranceItemDTO buildDeviceInsuranceItem(DeviceInsuranceBO bo) {
        List<Attachment> attachmentList = JsonUtils.readValue(bo.getPolicyAttachment(), new TypeReference<List<Attachment>>() {
        });
        String url = null;
        String fileKey = null;
        if (CollectionUtil.isNotEmpty(attachmentList)) {
            Attachment attachment = attachmentList.get(0);
            url = s3Manager.generateUrl(attachment.getBucketName(), attachment.getFileKey(), Constants.EXPIRE_TIME_MS);
            fileKey = attachment.getFileKey();
        }
        return DeviceInsuranceItemDTO.builder()
                .deviceInsuranceId(bo.getDeviceInsuranceId())
                .policyNumber(bo.getPolicyNumber())
                .provinceName(bo.getProvinceName())
                .cityName(bo.getCityName())
                .stationName(bo.getStationName())
                .effectiveStartTime(bo.getEffectiveStartTime())
                .effectiveEndTime(bo.getEffectiveEndTime())
                .url(url)
                .fileKey(fileKey).build();
    }
}
