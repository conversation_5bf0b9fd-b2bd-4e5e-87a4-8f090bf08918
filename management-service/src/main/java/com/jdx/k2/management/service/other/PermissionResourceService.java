package com.jdx.k2.management.service.other;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jdx.k2.management.common.enums.base.BaseResponseEnum;
import com.jdx.k2.management.common.utils.PermissionConvertUtil;
import com.jdx.k2.management.domain.dto.other.PermissionResourceDTO;
import com.jdx.k2.management.domain.po.other.Resource;
import com.jdx.k2.management.domain.po.other.RoleResource;
import com.jdx.k2.management.domain.vo.other.PermissionResourceAddVO;
import com.jdx.k2.management.domain.vo.other.PermissionResourceEditVO;
import com.jdx.k2.management.domain.vo.other.PermissionResourceListVO;
import com.jdx.k2.management.repository.mapper.other.ResourceMapper;
import com.jdx.k2.management.repository.mapper.other.RoleResourceMapper;
import com.jdx.rover.common.domain.page.PageDTO;
import com.jdx.rover.common.domain.page.PageVO;
import com.jdx.rover.jsf.util.JsfLoginUtil;
import com.jdx.rover.metadata.api.domain.enums.ResourceAppEnum;
import com.jdx.rover.metadata.api.domain.enums.ResourceTypeEnum;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 权限管理-资源管理-service
 *
 * <AUTHOR>
 */
@Service
public class PermissionResourceService {

    @Autowired
    private ResourceMapper resourceMapper;

    @Autowired
    private RoleResourceMapper roleResourceMapper;

    /**
     * 1、分页查询资源列表
     */
    public PageDTO<PermissionResourceDTO> getResourcePageList(PageVO pageVO, PermissionResourceListVO permissionResourceListVO) {
        // 查询顶级资源
        IPage<Resource> iPage = new Page<>(pageVO.getPageNum(), pageVO.getPageSize());
        LambdaQueryWrapper<Resource> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.isNull(Resource::getParentNumber);
        queryWrapper.like(StringUtils.isNotEmpty(permissionResourceListVO.getName()), Resource::getName, permissionResourceListVO.getName());
        queryWrapper.eq(permissionResourceListVO.getApp() != null, Resource::getApp, permissionResourceListVO.getApp());
        queryWrapper.like(StringUtils.isNotEmpty(permissionResourceListVO.getResourceCode()), Resource::getResourceCode, permissionResourceListVO.getResourceCode());
        queryWrapper.orderByAsc(Resource::getOrderBy);
        IPage<Resource> resultPage = resourceMapper.selectPage(iPage, queryWrapper);

        // 转换出参
        PageDTO<PermissionResourceDTO> pageDTO = new PageDTO<>();
        pageDTO.setPageNum((int) resultPage.getCurrent());
        pageDTO.setPageSize((int) resultPage.getSize());
        pageDTO.setPages((int) resultPage.getPages());
        pageDTO.setTotal(resultPage.getTotal());
        pageDTO.setList(PermissionConvertUtil.resourcePoList2DtoList(resultPage.getRecords()));

        // 补全子资源
        completeChildren(pageDTO.getList());

        return pageDTO;
    }

    /**
     * 1.1、补全子资源
     *
     * @param parentDTOList 父资源集合
     */
    private void completeChildren(List<PermissionResourceDTO> parentDTOList) {
        if (CollectionUtils.isEmpty(parentDTOList)) {
            return;
        }
        for (PermissionResourceDTO permissionResourceDTO : parentDTOList) {
            // 查询子资源
            LambdaQueryWrapper<Resource> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(Resource::getParentNumber, permissionResourceDTO.getNumber());
            queryWrapper.orderByAsc(Resource::getOrderBy);
            List<Resource> sonList = resourceMapper.selectList(queryWrapper);
            // 如果查不到子资源，则设置标识
            if (CollectionUtils.isEmpty(sonList)) {
                permissionResourceDTO.setHasChildren(false);
            }
            // 如果查到子资源，则设置标识和子资源数据
            else {
                permissionResourceDTO.setHasChildren(true);
                permissionResourceDTO.setChildren(PermissionConvertUtil.resourcePoList2DtoList(sonList));
                // 递归，继续查询下一级子资源
                completeChildren(permissionResourceDTO.getChildren());
            }
        }
    }

    /**
     * 2、新增资源
     */
    @Transactional(rollbackFor = Exception.class)
    public BaseResponseEnum addResource(PermissionResourceAddVO permissionResourceAddVO) {
        // 校验资源名称是否重复
        LambdaQueryWrapper<Resource> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Resource::getName, permissionResourceAddVO.getName());
        Long count = resourceMapper.selectCount(queryWrapper);
        if (count > 0) {
            return BaseResponseEnum.ERROR_PERM_RESOURCE_DUP_NAME;
        }

        //判断number是否重复
        LambdaQueryWrapper<Resource> resourceLambdaQueryWrapper = new LambdaQueryWrapper<>();
        resourceLambdaQueryWrapper.eq(Resource::getNumber, permissionResourceAddVO.getNumber());
        Long num = resourceMapper.selectCount(resourceLambdaQueryWrapper);
        if (num > 0) {
            return BaseResponseEnum.ERROR_PERM_RESOURCE_DUP_NUMBER;
        }

        // 校验资源标识是否重复
        LambdaQueryWrapper<Resource> resourceCodeQueryWrapper = new LambdaQueryWrapper<>();
        resourceCodeQueryWrapper.eq(Resource::getResourceCode, permissionResourceAddVO.getResourceCode());
        Long resourceCodeCount = resourceMapper.selectCount(resourceCodeQueryWrapper);
        if (resourceCodeCount > 0) {
            return BaseResponseEnum.ERROR_PERM_RESOURCE_DUP_CODE;
        }

        // 插入
        Resource resource = new Resource();
        BeanUtils.copyProperties(permissionResourceAddVO, resource);
        resourceMapper.insert(resource);

        return BaseResponseEnum.SUCCESS;
    }

    /**
     * 3、查询资源详情
     */
    public PermissionResourceDTO getResourceDetail(String resourceNumber) {
        // 查询
        LambdaQueryWrapper<Resource> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Resource::getNumber, resourceNumber);
        Resource resource = resourceMapper.selectOne(queryWrapper);

        // 转换出参
        PermissionResourceDTO permissionResourceDTO = new PermissionResourceDTO();
        BeanUtils.copyProperties(resource, permissionResourceDTO);
        permissionResourceDTO.setAppName(ResourceAppEnum.getNameByValue(permissionResourceDTO.getApp()));
        permissionResourceDTO.setTypeName(ResourceTypeEnum.getNameByValue(permissionResourceDTO.getType()));

        return permissionResourceDTO;
    }

    /**
     * 4、编辑资源
     */
    @Transactional(rollbackFor = Exception.class)
    public BaseResponseEnum editResource(PermissionResourceEditVO permissionResourceEditVO) {
        String username = JsfLoginUtil.getUsername();
        // 校验资源是否存在
        LambdaQueryWrapper<Resource> resourceNumberQueryWrapper = new LambdaQueryWrapper<>();
        resourceNumberQueryWrapper.eq(Resource::getNumber, permissionResourceEditVO.getNumber());
        Resource dbResource = resourceMapper.selectOne(resourceNumberQueryWrapper);
        if (dbResource == null) {
            return BaseResponseEnum.ERROR_PERM_RESOURCE_NULL;
        }

        // 如果资源名称变了，则校验资源名称是否重复
        if (!permissionResourceEditVO.getName().equals(dbResource.getName())) {
            LambdaQueryWrapper<Resource> resourceNameQueryWrapper = new LambdaQueryWrapper<>();
            resourceNameQueryWrapper.eq(Resource::getName, permissionResourceEditVO.getName());
            Long resourceNameCount = resourceMapper.selectCount(resourceNameQueryWrapper);
            if (resourceNameCount > 0) {
                return BaseResponseEnum.ERROR_PERM_RESOURCE_DUP_NAME;
            }
        }

        // 如果资源标识变了，则校验资源标识是否重复
        if (!permissionResourceEditVO.getResourceCode().equals(dbResource.getResourceCode())) {
            LambdaQueryWrapper<Resource> resourceCodeQueryWrapper = new LambdaQueryWrapper<>();
            resourceCodeQueryWrapper.eq(Resource::getResourceCode, permissionResourceEditVO.getResourceCode());
            Long resourceCodeCount = resourceMapper.selectCount(resourceCodeQueryWrapper);
            if (resourceCodeCount > 0) {
                return BaseResponseEnum.ERROR_PERM_RESOURCE_DUP_CODE;
            }
        }

        // 更新
        Resource resource = new Resource();
        BeanUtils.copyProperties(permissionResourceEditVO, resource);
        resource.setModifyUser(username);
        LambdaQueryWrapper<Resource> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Resource::getNumber, permissionResourceEditVO.getNumber());
        resourceMapper.update(resource, queryWrapper);

        return BaseResponseEnum.SUCCESS;
    }

    /**
     * 5、删除资源
     */
    @Transactional(rollbackFor = Exception.class)
    public BaseResponseEnum deleteResource(String resourceNumber) {
        // 资源及其各级子资源的编号集合：初始容量给大点，防止扩容
        List<String> allResourceNumberList = new ArrayList<>(1024);
        // 查询各级资源的编号
        getAllResourceNumberList(Collections.singletonList(resourceNumber), allResourceNumberList);

        // 如果资源与角色存在关联关系，则不允许删除
        LambdaQueryWrapper<RoleResource> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(RoleResource::getResourceNumber, allResourceNumberList);
        Long count = roleResourceMapper.selectCount(queryWrapper);
        if (count > 0) {
            return BaseResponseEnum.ERROR_PERM_RESOURCE_USING;
        }

        // 删除
        LambdaQueryWrapper<Resource> deleteWrapper = new LambdaQueryWrapper<>();
        deleteWrapper.in(Resource::getNumber, allResourceNumberList);
        resourceMapper.delete(deleteWrapper);

        return BaseResponseEnum.SUCCESS;
    }

    /**
     * 5.1、查询各级子资源的编号
     *
     * @param resourceNumberList    当前层级资源编号集合
     * @param allResourceNumberList 保存所有资源编号的集合
     */
    private void getAllResourceNumberList(List<String> resourceNumberList, List<String> allResourceNumberList) {
        if (CollectionUtils.isEmpty(resourceNumberList)) {
            return;
        }
        for (String resourceNumber : resourceNumberList) {
            allResourceNumberList.add(resourceNumber);
            // 根据资源编号查询下一级子资源
            LambdaQueryWrapper<Resource> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(Resource::getParentNumber, resourceNumber);
            List<Resource> sonResourceList = resourceMapper.selectList(queryWrapper);
            if (CollectionUtils.isEmpty(sonResourceList)) {
                continue;
            }
            // 如果查到子资源，则递归
            List<String> sonResourceNumberList = sonResourceList.stream().map(Resource::getNumber).collect(Collectors.toList());
            getAllResourceNumberList(sonResourceNumberList, allResourceNumberList);
        }
    }
}
