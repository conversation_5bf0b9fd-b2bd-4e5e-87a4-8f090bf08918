package com.jdx.k2.management.service.station;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jdx.k2.management.common.config.ProductKeyProperties;
import com.jdx.k2.management.common.enums.RequireStatusEnum;
import com.jdx.k2.management.common.enums.base.BaseResponseEnum;
import com.jdx.k2.management.common.exception.AppRuntimeException;
import com.jdx.k2.management.common.utils.BeanCopyUtils;
import com.jdx.k2.management.common.utils.PageUtil;
import com.jdx.k2.management.common.utils.ParameterCheckUtil;
import com.jdx.k2.management.domain.bo.GridSpecification;
import com.jdx.k2.management.domain.bo.StopVehicleDetailBO;
import com.jdx.k2.management.domain.bo.VehiclePointCountBO;
import com.jdx.k2.management.domain.bo.VehicleStopBasicBO;
import com.jdx.k2.management.domain.bo.VehicleStopInfoBO;
import com.jdx.k2.management.domain.dto.common.PointDTO;
import com.jdx.k2.management.domain.dto.lifecycle.GridDTO;
import com.jdx.k2.management.domain.dto.lifecycle.PalletDTO;
import com.jdx.k2.management.domain.dto.lifecycle.VehicleBoxDetailDTO;
import com.jdx.k2.management.domain.dto.station.StationVehicleListDTO;
import com.jdx.k2.management.domain.dto.station.VehicleBoxGridDTO;
import com.jdx.k2.management.domain.dto.station.JumpLinkStationStopDTO;
import com.jdx.k2.management.domain.dto.station.StationVehicleDetailDTO;
import com.jdx.k2.management.domain.dto.station.StationVehicleGetPageListDTO;
import com.jdx.k2.management.domain.dto.station.StationVehicleWhetherUnlinkDTO;
import com.jdx.k2.management.domain.dto.station.VehicleLinkPointDTO;
import com.jdx.k2.management.domain.po.device.Box;
import com.jdx.k2.management.domain.po.device.BoxTemplate;
import com.jdx.k2.management.domain.po.device.DeviceBaseInfo;
import com.jdx.k2.management.domain.po.device.DeviceTypeBaseInfo;
import com.jdx.k2.management.domain.po.device.Grid;
import com.jdx.k2.management.domain.po.device.Pallet;
import com.jdx.k2.management.domain.po.device.VehicleInfo;
import com.jdx.k2.management.domain.po.lifecycle.RequireInfo;
import com.jdx.k2.management.domain.po.station.StationBaseInfo;
import com.jdx.k2.management.domain.po.station.vehicle.Stop;
import com.jdx.k2.management.domain.po.station.vehicle.VehicleStop;
import com.jdx.k2.management.domain.vo.lifecycle.GridVO;
import com.jdx.k2.management.domain.vo.lifecycle.PalletVO;
import com.jdx.k2.management.domain.vo.station.EditStationVehicleBoxVO;
import com.jdx.k2.management.domain.vo.station.EditStationVehicleDetailVO;
import com.jdx.k2.management.domain.vo.station.StationVehicleBindStopBatchVO;
import com.jdx.k2.management.domain.vo.station.StationVehicleGetPageListVO;
import com.jdx.k2.management.manager.feign.IntelligentDeviceServerDeviceManager;
import com.jdx.k2.management.manager.feign.ScheduleWebFeignManager;
import com.jdx.k2.management.manager.jmq.JmqBoxProducer;
import com.jdx.k2.management.manager.jmq.JmqDeviceProducer;
import com.jdx.k2.management.manager.jmq.JmqStopProducer;
import com.jdx.k2.management.repository.mapper.device.BoxMapper;
import com.jdx.k2.management.repository.mapper.device.BoxTemplateMapper;
import com.jdx.k2.management.repository.mapper.device.DeviceBaseInfoMapper;
import com.jdx.k2.management.repository.mapper.device.DeviceTypeBaseInfoMapper;
import com.jdx.k2.management.repository.mapper.device.GridMapper;
import com.jdx.k2.management.repository.mapper.device.PalletMapper;
import com.jdx.k2.management.repository.mapper.device.VehicleInfoMapper;
import com.jdx.k2.management.repository.mapper.lifecycle.RequireInfoMapper;
import com.jdx.k2.management.repository.mapper.station.StationBaseInfoMapper;
import com.jdx.k2.management.repository.mapper.station.StopMapper;
import com.jdx.k2.management.repository.mapper.station.VehicleStopMapper;
import com.jdx.rover.common.domain.page.PageDTO;
import com.jdx.rover.common.domain.page.PageVO;
import com.jdx.rover.jsf.util.JsfLoginUtil;
import com.jdx.rover.metadata.api.domain.enums.ProductTypeEnum;
import com.jdx.rover.metadata.api.domain.enums.StopTypeEnum;
import com.jdx.rover.metadata.api.domain.enums.VehicleBusinessTypeEnum;
import com.jdx.rover.metadata.api.domain.enums.VehicleHardwareStatusEnum;
import com.jdx.rover.metadata.api.domain.enums.VehicleOwnerUseCaseEnum;
import com.jdx.rover.metadata.api.domain.enums.VehicleStageEnum;
import com.jdx.rover.metadata.api.domain.enums.VehicleUseCaseEnum;
import com.jdx.rover.metadata.api.domain.enums.VideoModeEnum;
import com.jdx.rover.metadata.api.domain.enums.common.EnableEnum;
import com.jdx.rover.metadata.api.domain.enums.common.YesOrNoEnum;
import com.jdx.rover.metadata.api.domain.enums.kafka.OperationTypeEnum;
import com.jdx.rover.schedule.api.domain.dto.metadata.ScheduleInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.CollectionUtils;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import static com.jdx.k2.management.common.constants.Constants.DELETED;

/**
 * <AUTHOR>
 * @description: 站点下车辆列表信息
 * @date 2024年03月08日
 * @version: 1.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class StationVehicleService {

    private final DeviceBaseInfoMapper deviceBaseInfoMapper;

    private final VehicleInfoMapper vehicleInfoMapper;
    
    private final StationBaseInfoMapper stationBaseInfoMapper;
    
    private final VehicleStopMapper vehicleStopMapper;
    
    private final BoxMapper boxMapper;

    private final GridMapper gridMapper;
    
    private final BoxTemplateMapper boxTemplateMapper;

    private final PalletMapper palletMapper;
    
    private final DeviceTypeBaseInfoMapper deviceTypeBaseInfoMapper;
    
    private final StopMapper stopMapper;
    
    private final ScheduleWebFeignManager scheduleWebFeignManager;
    
    private final RequireInfoMapper requireInfoMapper;

    private final JmqDeviceProducer jmqDeviceProducer;

    private final JmqBoxProducer jmqBoxProducer;

    private final JmqStopProducer jmqStopProducer;

    private final IntelligentDeviceServerDeviceManager intelligentDeviceServerDeviceManager;

    private final ProductKeyProperties productKeyProperties;


    /**
     * @param pageVO:
     * @param stationVehicleGetPageListVO:
     * @return StationVehicleGetPageListDTO
     * @description 分页获取站点绑定的车辆信息列表
     * <AUTHOR>
     * @date 2024/3/8 14:45
     */
    public PageDTO<StationVehicleGetPageListDTO> getStationVehiclePageList(PageVO pageVO, StationVehicleGetPageListVO stationVehicleGetPageListVO) {
        // 查询
        IPage<DeviceBaseInfo> iPage = new Page<>(pageVO.getPageNum(), pageVO.getPageSize());
        QueryWrapper<DeviceBaseInfo> deviceBaseInfoQueryWrapper = new QueryWrapper<>();
        deviceBaseInfoQueryWrapper.eq("dbi.station_base_id", stationVehicleGetPageListVO.getStationBaseId());
        deviceBaseInfoQueryWrapper.like(StringUtils.isNotBlank(stationVehicleGetPageListVO.getDeviceName()), "dbi.name", stationVehicleGetPageListVO.getDeviceName());
        deviceBaseInfoQueryWrapper.like(StringUtils.isNotBlank(stationVehicleGetPageListVO.getSerialNo()), "dbi.serial_no", stationVehicleGetPageListVO.getSerialNo());
        deviceBaseInfoQueryWrapper.eq(StringUtils.isNotBlank(stationVehicleGetPageListVO.getProductType()), "dbi.product_type", stationVehicleGetPageListVO.getProductType());
        deviceBaseInfoQueryWrapper.eq(StringUtils.isNotBlank(stationVehicleGetPageListVO.getBusinessType()), "dbi.business_type", stationVehicleGetPageListVO.getBusinessType());
        deviceBaseInfoQueryWrapper.eq(Objects.nonNull(stationVehicleGetPageListVO.getDeviceTypeBaseId()), "dbi.device_type_base_id", stationVehicleGetPageListVO.getDeviceTypeBaseId());
        deviceBaseInfoQueryWrapper.eq(Objects.nonNull(stationVehicleGetPageListVO.getIsRequire()), "dbi.is_require", stationVehicleGetPageListVO.getIsRequire());
        deviceBaseInfoQueryWrapper.eq(Objects.nonNull(stationVehicleGetPageListVO.getIsVirtual()), "dbi.is_virtual", stationVehicleGetPageListVO.getIsVirtual());
        deviceBaseInfoQueryWrapper.eq("dbi.deleted", DELETED);
        deviceBaseInfoQueryWrapper.orderByDesc("dbi.modify_time");
        IPage<StationVehicleGetPageListDTO> stationVehicleGetPageListDTOIPage = deviceBaseInfoMapper.selectStationVehiclePageByParams(iPage, deviceBaseInfoQueryWrapper);
        List<StationVehicleGetPageListDTO> stationVehicleGetPageListDTOList = stationVehicleGetPageListDTOIPage.getRecords();
        if (!CollectionUtils.isEmpty(stationVehicleGetPageListDTOList)) {
            //查询车辆绑定的跨站停靠点数量和本站停靠点数量
            Set<Integer> deviceBaseIdSet = stationVehicleGetPageListDTOList.stream().map(StationVehicleGetPageListDTO::getDeviceBaseId).collect(Collectors.toSet());
            Map<Integer, VehiclePointCountBO> vehicleStopCountMap = getVehicleStopCount(stationVehicleGetPageListVO.getStationBaseId(), deviceBaseIdSet);
            stationVehicleGetPageListDTOList.forEach(stationVehicleGetPageListDTO -> {
                //产品类型名称
                stationVehicleGetPageListDTO.setProductTypeName(ProductTypeEnum.getNameByValue(stationVehicleGetPageListDTO.getProductType()));
                //设备归属方名称
                stationVehicleGetPageListDTO.setOwnerUseCaseName(VehicleOwnerUseCaseEnum.getNameByValue(stationVehicleGetPageListDTO.getOwnerUseCase()));
                //设备类型
                stationVehicleGetPageListDTO.setBusinessTypeName(VehicleBusinessTypeEnum.getNameByValue(stationVehicleGetPageListDTO.getBusinessType()));
                //设备阶段名称
                stationVehicleGetPageListDTO.setVehicleStage(VehicleStageEnum.getNameByValue(stationVehicleGetPageListDTO.getVehicleStage()));
                //设备生命周期名称
                stationVehicleGetPageListDTO.setHardwareStatusName(VehicleHardwareStatusEnum.getNameByValue(stationVehicleGetPageListDTO.getHardwareStatus()));
                //推流模式名称
                stationVehicleGetPageListDTO.setVideoModeName(VideoModeEnum.getNameByValue(stationVehicleGetPageListDTO.getVideoMode()));
                //是否在维修名称
                stationVehicleGetPageListDTO.setIsVirtualName(YesOrNoEnum.getNameByValue(stationVehicleGetPageListDTO.getIsVirtual()));
                //是否是虚拟车名称
                stationVehicleGetPageListDTO.setIsRequireName(YesOrNoEnum.getNameByValue(stationVehicleGetPageListDTO.getIsRequire()));
                //绑定的停靠点信息
                VehiclePointCountBO vehiclePointCountBO = vehicleStopCountMap.get(stationVehicleGetPageListDTO.getDeviceBaseId());
                if (Objects.nonNull(vehiclePointCountBO)) {
                    stationVehicleGetPageListDTO.setLinkJumpStationStopCount(vehiclePointCountBO.getJumpPointCount());
                    stationVehicleGetPageListDTO.setLinkLocalStationStopCount(vehiclePointCountBO.getLocalPointCount());
                }
            });
        }

        return PageUtil.toPageDTO(stationVehicleGetPageListDTOIPage, stationVehicleGetPageListDTOList);
    }

    /**
     * @param deviceBaseId:
     * @return StationVehicleDetailDTO
     * @description 获取站点绑定的无人车详情信息
     * <AUTHOR>
     * @date 2024/3/9 13:42
     */
    public StationVehicleDetailDTO getStationVehicleDetail(Integer deviceBaseId) {
        //检查
        ParameterCheckUtil.checkNotNull(deviceBaseId, BaseResponseEnum.ERROR_VEHICLE_ID.getMessage());
        ParameterCheckUtil.checkPositive(deviceBaseId, BaseResponseEnum.ERROR_VEHICLE_ID.getMessage());
        //获取设备基础信息
        DeviceBaseInfo deviceBaseInfo = deviceBaseInfoMapper.selectById(deviceBaseId);
        //获取车型信息
        DeviceTypeBaseInfo deviceTypeBaseInfo = deviceTypeBaseInfoMapper.selectById(deviceBaseInfo.getDeviceTypeBaseId());
        //获取站点信息
        StationBaseInfo stationBaseInfo = stationBaseInfoMapper.selectById(deviceBaseInfo.getStationBaseId());
        //获取箱体模板
        BoxTemplate boxTemplate = boxTemplateMapper.selectById(deviceBaseInfo.getBoxTemplateId());
        //获取格口信息（启用）
        Box box = boxMapper.selectById(deviceBaseInfo.getBoxId());
        LambdaQueryWrapper<Grid> gridLambdaQueryWrapper = new LambdaQueryWrapper<>();
        gridLambdaQueryWrapper.eq(Grid::getBoxId, box.getId());
        gridLambdaQueryWrapper.eq(Grid::getEnable, EnableEnum.ENABLE.getEnable());
        List<Grid> grids = gridMapper.selectList(gridLambdaQueryWrapper);
        List<VehicleBoxGridDTO> gridList = new ArrayList<>();
        for (Grid grid : grids) {
            VehicleBoxGridDTO vehicleBoxGridDTO = new VehicleBoxGridDTO();
            vehicleBoxGridDTO.setId(grid.getId());
            vehicleBoxGridDTO.setGridNo(grid.getGridNo());
            vehicleBoxGridDTO.setLength(grid.getLength());
            vehicleBoxGridDTO.setWidth(grid.getWidth());
            vehicleBoxGridDTO.setHeight(grid.getHeight());
            vehicleBoxGridDTO.setVolume(grid.getLength() * grid.getWidth() * grid.getHeight() / 1000);
            // 获取小格口托盘
            LambdaQueryWrapper<Pallet> palletLambdaQueryWrapper = new LambdaQueryWrapper<>();
            palletLambdaQueryWrapper.eq(Pallet::getGridId, grid.getId());
            palletLambdaQueryWrapper.eq(Pallet::getEnable, EnableEnum.ENABLE.getEnable());
            List<Pallet> palletList = palletMapper.selectList(palletLambdaQueryWrapper);
            if (palletList.isEmpty()) {
                vehicleBoxGridDTO.setPallet(null);
            } else {
                StringBuilder sb = new StringBuilder();
                for (Pallet pallet : palletList) {
                    sb.append(pallet.getName());
                    sb.append(";");
                }
                String pallet = sb.substring(0, sb.length() - 1);
                vehicleBoxGridDTO.setPallet(pallet);
            }
            gridList.add(vehicleBoxGridDTO);
        }

        //组装DTO
        StationVehicleDetailDTO stationVehicleDetailDTO = new StationVehicleDetailDTO();

        stationVehicleDetailDTO.setCityId(stationBaseInfo.getCityId());
        stationVehicleDetailDTO.setStationBaseId(stationBaseInfo.getId());
        stationVehicleDetailDTO.setStationName(stationBaseInfo.getName());
        stationVehicleDetailDTO.setDeviceBaseId(deviceBaseId);
        stationVehicleDetailDTO.setDeviceName(deviceBaseInfo.getName());
        stationVehicleDetailDTO.setSerialNo(deviceBaseInfo.getSerialNo());
        stationVehicleDetailDTO.setProductType(deviceBaseInfo.getProductType());
        stationVehicleDetailDTO.setProductTypeName(ProductTypeEnum.getNameByValue(deviceBaseInfo.getProductType()));
        stationVehicleDetailDTO.setDeviceTypeBaseId(deviceBaseInfo.getDeviceTypeBaseId());
        stationVehicleDetailDTO.setDeviceTypeName(deviceTypeBaseInfo.getName());
        stationVehicleDetailDTO.setBusinessType(deviceBaseInfo.getBusinessType());
        stationVehicleDetailDTO.setBusinessTypeName(VehicleBusinessTypeEnum.getNameByValue(deviceBaseInfo.getBusinessType()));
        stationVehicleDetailDTO.setBoxTemplateId(deviceBaseInfo.getBoxTemplateId());
        stationVehicleDetailDTO.setBoxTemplateName(boxTemplate.getName());
        stationVehicleDetailDTO.setBoxId(deviceBaseInfo.getBoxId());
        stationVehicleDetailDTO.setGridList(gridList);
        stationVehicleDetailDTO.setVCode(deviceBaseInfo.getVCode());

        //关联的停靠点信息
        List<VehicleLinkPointDTO> linkPointList = new ArrayList<>();
        List<JumpLinkStationStopDTO> jumpStationStopList = new ArrayList<>();

        //查询出该站点下所有的停靠点
        LambdaQueryWrapper<Stop> stopLambdaQueryWrapper = new LambdaQueryWrapper<>();
        stopLambdaQueryWrapper.eq(Stop::getStationId, stationBaseInfo.getId());
        stopLambdaQueryWrapper.eq(Stop::getEnable, EnableEnum.ENABLE.getEnable());
        List<Stop> localStationAllStopList = stopMapper.selectList(stopLambdaQueryWrapper);
        //按类型分组
        Map<String, List<Stop>> stopTypeStopListMap = localStationAllStopList.stream().collect(Collectors.groupingBy(Stop::getType));

        //获取该车绑定的所有停靠点
        QueryWrapper<VehicleStop> vehicleStopQueryWrapper = new QueryWrapper<>();
        vehicleStopQueryWrapper.eq("vs.vehicle_id", deviceBaseId);
        vehicleStopQueryWrapper.eq("vs.deleted", DELETED);
        List<VehicleStopInfoBO> vehicleStopInfoBOList = vehicleStopMapper.selectVehicleStop(vehicleStopQueryWrapper);
        //绑定的本站停靠点
        List<VehicleStopInfoBO> bindLocalStopList = vehicleStopInfoBOList.stream().filter(vehicleStopInfoBO -> vehicleStopInfoBO.getStationBaseId().equals(stationBaseInfo.getId())).collect(Collectors.toList());
        //按类型分组
        Map<String, List<VehicleStopInfoBO>> bindLocalStopTypeListMap = bindLocalStopList.stream().collect(Collectors.groupingBy(VehicleStopInfoBO::getStopType));

        stopTypeStopListMap.forEach((type, typeStopList) -> {
            VehicleLinkPointDTO vehicleLinkPointDTO = new VehicleLinkPointDTO();
            vehicleLinkPointDTO.setStopType(type);
            vehicleLinkPointDTO.setStopTypeName(StopTypeEnum.getNameByValue(type));
            List<PointDTO> stopList = new ArrayList<>();
            List<Stop> notBindTypeStopList = null;
            List<VehicleStopInfoBO> bindTypeStopList = bindLocalStopTypeListMap.get(type);
            if (!CollectionUtils.isEmpty(bindTypeStopList)) {
                bindTypeStopList.forEach(vehicleStopInfoBO -> {
                    PointDTO pointDTO = new PointDTO();
                    pointDTO.setId(vehicleStopInfoBO.getStopId());
                    pointDTO.setName(vehicleStopInfoBO.getStopName());
                    pointDTO.setIsLinked(true);
                    pointDTO.setWaitingTime(vehicleStopInfoBO.getWaitingTime());
                    stopList.add(pointDTO);
                });
                Set<Integer> bindTypeStopIdSet = bindTypeStopList.stream().map(VehicleStopInfoBO::getStopId).collect(Collectors.toSet());
                notBindTypeStopList = typeStopList.stream().filter(stop -> !bindTypeStopIdSet.contains(stop.getId())).collect(Collectors.toList());
            } else {
                notBindTypeStopList = typeStopList;
            }
            notBindTypeStopList.forEach(stop -> {
                PointDTO pointDTO = new PointDTO();
                pointDTO.setId(stop.getId());
                pointDTO.setName(stop.getName());
                pointDTO.setIsLinked(false);
                pointDTO.setWaitingTime(stop.getWaitingTime());
                stopList.add(pointDTO);
            });
            vehicleLinkPointDTO.setStopList(stopList);
            linkPointList.add(vehicleLinkPointDTO);
        });

        //绑定的跨站停靠点
        List<VehicleStopInfoBO> bindJumpStopList = vehicleStopInfoBOList.stream().filter(vehicleStopInfoBO -> !vehicleStopInfoBO.getStationBaseId().equals(stationBaseInfo.getId())).collect(Collectors.toList());

        Map<Integer, List<VehicleStopInfoBO>> stationBaseIdStopListMap = bindJumpStopList.stream().collect(Collectors.groupingBy(VehicleStopInfoBO::getStationBaseId));
        stationBaseIdStopListMap.forEach((stationBaseId, stationVehicleStopInfoBOList) -> {
            JumpLinkStationStopDTO jumpLinkStationStopDTO = new JumpLinkStationStopDTO();
            jumpLinkStationStopDTO.setStationBaseId(stationBaseId);
            jumpLinkStationStopDTO.setSort(stationBaseId);
            List<VehicleLinkPointDTO> vehicleLinkPointDTOList = new ArrayList<>();
            //按类型分
            Map<String, List<VehicleStopInfoBO>> typeStopInfoListMap = stationVehicleStopInfoBOList.stream().collect(Collectors.groupingBy(VehicleStopInfoBO::getStopType));
            typeStopInfoListMap.forEach((type, typeStopList) -> {
                VehicleLinkPointDTO vehicleLinkPointDTO = new VehicleLinkPointDTO();
                vehicleLinkPointDTO.setStopType(type);
                vehicleLinkPointDTO.setStopTypeName(StopTypeEnum.getNameByValue(type));
                List<PointDTO> stopList = new ArrayList<>();
                typeStopList.forEach(vehicleStopInfoBO -> {
                    PointDTO pointDTO = new PointDTO();
                    pointDTO.setId(vehicleStopInfoBO.getStopId());
                    pointDTO.setName(vehicleStopInfoBO.getStopName());
                    pointDTO.setIsLinked(true);
                    pointDTO.setWaitingTime(vehicleStopInfoBO.getWaitingTime());
                    stopList.add(pointDTO);
                });
                vehicleLinkPointDTO.setStopList(stopList);
                vehicleLinkPointDTOList.add(vehicleLinkPointDTO);
            });
            jumpLinkStationStopDTO.setLinkStopList(vehicleLinkPointDTOList);
            jumpStationStopList.add(jumpLinkStationStopDTO);
        });
        stationVehicleDetailDTO.setLinkPointList(linkPointList);
        stationVehicleDetailDTO.setJumpStationStopList(jumpStationStopList);
        return stationVehicleDetailDTO;
    }

    /**
     * @param deviceBaseId:
     * @return VehicleBoxDetailDTO
     * @description 获取车辆货箱详情
     * <AUTHOR>
     * @date 2024/3/9 13:42
     */
    public VehicleBoxDetailDTO getVehicleBoxDetail(Integer deviceBaseId) {
        // 检查
        ParameterCheckUtil.checkNotNull(deviceBaseId, BaseResponseEnum.ERROR_VEHICLE_ID.getMessage());
        ParameterCheckUtil.checkPositive(deviceBaseId, BaseResponseEnum.ERROR_VEHICLE_ID.getMessage());
        // 校验
        DeviceBaseInfo deviceBaseInfo = deviceBaseInfoMapper.selectById(deviceBaseId);
        ParameterCheckUtil.checkNotNull(deviceBaseInfo, BaseResponseEnum.ERROR_GET_VEHICLE.getMessage());
        // 获取车辆详情
        Box box = boxMapper.selectById(deviceBaseInfo.getBoxId());
        // 获取箱体模板
        BoxTemplate boxTemplate = boxTemplateMapper.selectById(deviceBaseInfo.getBoxTemplateId());
        // 获取格口信息
        LambdaQueryWrapper<Grid> gridLambdaQueryWrapper = new LambdaQueryWrapper<>();
        gridLambdaQueryWrapper.eq(Grid::getBoxId, box.getId());
//    gridLambdaQueryWrapper.eq(Grid::getEnable,EnableEnum.ENABLE.getEnable());
        List<Grid> grids = gridMapper.selectList(gridLambdaQueryWrapper);
        List<GridDTO> gridList = new ArrayList<>();
        Set<GridSpecification> specifications = new HashSet<>();
        for (Grid grid : grids) {
            GridDTO gridDTO = new GridDTO();
            gridDTO.setId(grid.getId());
            gridDTO.setGridNo(grid.getGridNo());
            gridDTO.setBoardNo(grid.getBoardNo());
            gridDTO.setSide(grid.getSide());
            gridDTO.setLockNo(grid.getLockNo());
            gridDTO.setLength(grid.getLength());
            gridDTO.setWidth(grid.getWidth());
            gridDTO.setHeight(grid.getHeight());
            gridDTO.setEnable(grid.getEnable());
            gridDTO.setEnableName(EnableEnum.getNameByEnable(grid.getEnable()));
            //统计格口规格
            GridSpecification gridSpecification = new GridSpecification();
            gridSpecification.setLength(grid.getLength());
            gridSpecification.setWidth(grid.getWidth());
            gridSpecification.setHeight(grid.getHeight());
            specifications.add(gridSpecification);
            // 小格口托盘
            List<PalletDTO> palletList = new ArrayList<>();
            LambdaQueryWrapper<Pallet> palletLambdaQueryWrapper = new LambdaQueryWrapper<>();
            palletLambdaQueryWrapper.eq(Pallet::getGridId, grid.getId());
            palletLambdaQueryWrapper.eq(Pallet::getEnable, EnableEnum.ENABLE.getEnable());
            List<Pallet> pallets = palletMapper.selectList(palletLambdaQueryWrapper);
            for (Pallet pallet : pallets) {
                PalletDTO palletDTO = new PalletDTO();
                palletDTO.setId(pallet.getId());
                palletDTO.setName(pallet.getName());
                palletList.add(palletDTO);
            }
            gridDTO.setPalletList(palletList);

            gridList.add(gridDTO);
        }
        // 组装
        VehicleBoxDetailDTO vehicleBoxDetailDTO = new VehicleBoxDetailDTO();
        vehicleBoxDetailDTO.setId(box.getId());
        vehicleBoxDetailDTO.setBoxTemplateId(boxTemplate.getId());
        vehicleBoxDetailDTO.setBoxTemplateName(boxTemplate.getName());
        vehicleBoxDetailDTO.setModel(box.getModel());
        vehicleBoxDetailDTO.setSerialNo(box.getSerialNo());
        vehicleBoxDetailDTO.setDriverType(box.getDriverType());
        vehicleBoxDetailDTO.setDeviceId(box.getDeviceId());
        vehicleBoxDetailDTO.setBaudRate(box.getBaudRate());
        vehicleBoxDetailDTO.setLeftBoxColumnNum(boxTemplate.getLeftBoxColumnNum());
        vehicleBoxDetailDTO.setRightBoxColumnNum(boxTemplate.getRightBoxColumnNum());
        vehicleBoxDetailDTO.setGridList(gridList);
        vehicleBoxDetailDTO.setGridSpecificationList(new ArrayList<>(specifications));
        return vehicleBoxDetailDTO;
    }

    /**
     * @param editStationVehicleBoxVO:
     * @return BaseResponseEnum
     * @description 编辑车辆货箱配置
     * <AUTHOR>
     * @date 2024/3/9 13:43
     */
    @Transactional(rollbackFor = Exception.class)
    public BaseResponseEnum editStationVehicleBox(EditStationVehicleBoxVO editStationVehicleBoxVO) {
        String username = JsfLoginUtil.getUsername();
        // 校验格口号唯一
        ParameterCheckUtil.checkBoxGridNo(editStationVehicleBoxVO.getGridList(), BaseResponseEnum.ERROR_BOX_GRID_NO.getMessage());

        //每次更改格口:1.新建box表数据;2更新device_base_info表box_id字段和box_template_id字段;3.新建grid表和pallet表数据
        //新建box表的信息
        List<GridVO> gridList = editStationVehicleBoxVO.getGridList();
        DeviceBaseInfo deviceBaseInfo = deviceBaseInfoMapper.selectById(editStationVehicleBoxVO.getDeviceBaseId());
        BoxTemplate boxTemplate = boxTemplateMapper.selectById(editStationVehicleBoxVO.getBoxTemplateId());
        Box newBox = new Box();
        newBox.setGridNum(gridList.size());
        newBox.setSerialNo(deviceBaseInfo.getSerialNo());
        newBox.setModel(boxTemplate.getHardwareModelModel());
        newBox.setLeftBoxColumnNum(boxTemplate.getLeftBoxColumnNum());
        newBox.setRightBoxColumnNum(boxTemplate.getRightBoxColumnNum());
        newBox.setBaudRate(boxTemplate.getBaudRate());
        newBox.setDeviceId(boxTemplate.getDeviceId());
        newBox.setDriverType(boxTemplate.getDriverType());
        newBox.setCreateUser(username);
        boxMapper.insert(newBox);
        //更新device_base_info
        DeviceBaseInfo updateDeviceBaseInfo = new DeviceBaseInfo();
        updateDeviceBaseInfo.setId(editStationVehicleBoxVO.getDeviceBaseId());
        updateDeviceBaseInfo.setBoxId(newBox.getId());
        updateDeviceBaseInfo.setBoxTemplateId(editStationVehicleBoxVO.getBoxTemplateId());
        deviceBaseInfoMapper.updateById(updateDeviceBaseInfo);
        //创建Grid
        for (GridVO gridVO : gridList) {
            Grid grid = new Grid();
            grid.setBoxId(newBox.getId());
            grid.setGridNo(gridVO.getGridNo());
            grid.setLength(gridVO.getLength());
            grid.setWidth(gridVO.getWidth());
            grid.setHeight(gridVO.getHeight());
            grid.setSide(gridVO.getSide());
            grid.setLockNo(gridVO.getLockNo());
            grid.setEnable(gridVO.getStatusId());
            grid.setBoardNo(gridVO.getBoardNo());
            grid.setCreateUser(username);
            gridMapper.insert(grid);
            // 创建小格口托盘
            List<PalletVO> palletList = gridVO.getPalletList();
            // 校验格口下小格口托盘唯一
            ParameterCheckUtil.checkPalletUniform(palletList, BaseResponseEnum.ERROR_BOX_GRID_PALLET.getMessage());
            for (PalletVO palletVO : palletList) {
                Pallet pallet = new Pallet();
                pallet.setGridId(grid.getId());
                pallet.setName(palletVO.getName());
                pallet.setEnable(EnableEnum.ENABLE.getEnable());
                pallet.setModifyUser(username);
                pallet.setCreateUser(username);
                palletMapper.insert(pallet);
            }
        }
        //货箱格口变更发送消息
        if (TransactionSynchronizationManager.isSynchronizationActive()) {
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    //新版消息发生变化
                    log.info("调整格口,货箱发生变化,vehicleName:{}", deviceBaseInfo.getName());
                    jmqBoxProducer.sendMetadataBoxMessage(newBox.getId(), deviceBaseInfo.getName(), deviceBaseInfo.getProductType(), deviceBaseInfo.getId());
                }
            });
        }
        return BaseResponseEnum.SUCCESS;
    }

    /**
     * @param editStationVehicleDetailVO:
     * @return BaseResponseEnum
     * @description 车辆配置确定
     * <AUTHOR>
     * @date 2024/3/9 13:43
     */
    @Transactional(rollbackFor = Exception.class)
    public BaseResponseEnum editStationVehicleDetail(EditStationVehicleDetailVO editStationVehicleDetailVO) {
        //检查
        DeviceBaseInfo deviceBaseInfo = deviceBaseInfoMapper.selectById(editStationVehicleDetailVO.getDeviceBaseId());
        Integer oldStationId = deviceBaseInfo.getStationBaseId();
        //更新设备基础信息
        if (StringUtils.isNotBlank(editStationVehicleDetailVO.getVCode())) {
            LambdaUpdateWrapper<DeviceBaseInfo> deviceBaseInfoLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
            deviceBaseInfoLambdaUpdateWrapper.eq(DeviceBaseInfo::getId, deviceBaseInfo.getId());
            deviceBaseInfoLambdaUpdateWrapper.set(DeviceBaseInfo::getVCode, editStationVehicleDetailVO.getVCode());
            deviceBaseInfoMapper.update(null, deviceBaseInfoLambdaUpdateWrapper);
        }

        // 更新关联点位
        //储存旧的点位关系
        List<VehicleStopBasicBO> oldVehicleStopBasicBOList = vehicleStopMapper.selectVehicleStopByVehicleId(deviceBaseInfo.getId());
        //重新关联车辆-点位
        //先将原先的关系删除
        LambdaQueryWrapper<VehicleStop> delete = new LambdaQueryWrapper<>();
        delete.eq(VehicleStop::getVehicleId, deviceBaseInfo.getId());
        vehicleStopMapper.delete(delete);
        //重新添加新的关系
        List<Integer> linkedStopList = editStationVehicleDetailVO.getLinkedStopList();
        linkedStopList.add(editStationVehicleDetailVO.getHomeStopId());
        //获取停靠点信息
        LambdaQueryWrapper<Stop> stopLambdaQueryWrapper = new LambdaQueryWrapper<>();
        stopLambdaQueryWrapper.in(Stop::getId, linkedStopList);
        List<Stop> stopList = stopMapper.selectList(stopLambdaQueryWrapper);
        stopList.forEach(stop -> {
            VehicleStop vehicleStop = new VehicleStop();
            vehicleStop.setVehicleId(deviceBaseInfo.getId());
            vehicleStop.setStopId(stop.getId());
            vehicleStop.setWaitingTime(stop.getWaitingTime());
            vehicleStopMapper.insert(vehicleStop);
        });
        //提交完成后发送消息
        if (TransactionSynchronizationManager.isSynchronizationActive()) {
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    //发送新版消息
                    log.info("车辆交付+车辆调度-车辆配置,发送消息变化,vehicleName:{}", deviceBaseInfo.getName());
                    jmqDeviceProducer.sendMetadataVehicleMessage(OperationTypeEnum.EDIT, deviceBaseInfo.getName());
                    //车点关系发生变化
                    List<StopVehicleDetailBO> oldVehicleStop = BeanCopyUtils.copyListProperties(oldVehicleStopBasicBOList, StopVehicleDetailBO::new);
                    List<VehicleStopBasicBO> newVehicleStopBasicBOS = vehicleStopMapper.selectVehicleStopByVehicleId(deviceBaseInfo.getId());
                    List<StopVehicleDetailBO> newVehicleStop = BeanCopyUtils.copyListProperties(newVehicleStopBasicBOS, StopVehicleDetailBO::new);
                    jmqStopProducer.sendVehicleStopMessage(oldStationId, deviceBaseInfo, oldVehicleStop, null, newVehicleStop, OperationTypeEnum.EDIT);
                }
            });
        }
        return BaseResponseEnum.SUCCESS;
    }

    /**
     * @param deviceBaseId:
     * @return StationVehicleWhetherUnlinkDTO
     * @description 断车辆是否可以解绑
     * <AUTHOR>
     * @date 2024/3/9 13:43
     */
    public StationVehicleWhetherUnlinkDTO vehicleWhetherUnlink(Integer deviceBaseId) {
        DeviceBaseInfo deviceBaseInfo = deviceBaseInfoMapper.selectById(deviceBaseId);
        if (deviceBaseInfo == null) {
            throw new AppRuntimeException(BaseResponseEnum.ERROR_GET_VEHICLE.getMessage());
        }
        //获取车辆的调度信息
        ScheduleInfo scheduleInfo = scheduleWebFeignManager.getVehicleScheduleInfo(deviceBaseInfo.getName());
        if (Objects.isNull(scheduleInfo)) {
            throw new AppRuntimeException(BaseResponseEnum.ERROR_CALL_SCHEDULE_GET_VEHICLE_SCHEDULE_INFO.getMessage());
        }
        StationVehicleWhetherUnlinkDTO stationVehicleWhetherUnlinkDTO = new StationVehicleWhetherUnlinkDTO();
        BeanUtils.copyProperties(scheduleInfo, stationVehicleWhetherUnlinkDTO);
        return stationVehicleWhetherUnlinkDTO;
    }

    /**
     * @param deviceBaseId:
     * @return BaseResponseEnum
     * @description 解绑
     * <AUTHOR>
     * @date 2024/3/9 13:43
     */
    @Transactional(rollbackFor = Exception.class)
    public BaseResponseEnum unlinkVehicleStation(Integer deviceBaseId) {
        //获取原始的车辆信息
        DeviceBaseInfo deviceBaseInfo = deviceBaseInfoMapper.selectById(deviceBaseId);
        StationBaseInfo stationBaseInfo = stationBaseInfoMapper.selectById(deviceBaseInfo.getStationBaseId());

        //通知调度清空调度
        Boolean booleanHttpResult = scheduleWebFeignManager.cleanSchedule(deviceBaseInfo.getName());
        if (booleanHttpResult == null || !booleanHttpResult) {//清空调度失败
            return BaseResponseEnum.ERROR_CALL_SCHEDULE_CLEAN_SCHEDULE;
        }

        //储存旧的站点信息
        List<VehicleStopBasicBO> oldVehicleStopBasicBOS = vehicleStopMapper.selectVehicleStopByVehicleId(deviceBaseInfo.getId());

        //删除关联点位
        LambdaQueryWrapper<VehicleStop> vehicleStopLambdaQueryWrapper = new LambdaQueryWrapper<>();
        vehicleStopLambdaQueryWrapper.eq(VehicleStop::getVehicleId, deviceBaseId);
        vehicleStopMapper.delete(vehicleStopLambdaQueryWrapper);

        // 更新车辆站点(去掉)生命周期(解绑待接收),车辆归属方(调度),车辆阶段(调度),车辆用途(测试),到站时间(置为NULL),交付时间(置为NULL)
        deviceBaseInfoMapper.updateUnlinkById(deviceBaseId);

        LambdaUpdateWrapper<VehicleInfo> vehicleInfoLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        vehicleInfoLambdaUpdateWrapper.eq(VehicleInfo::getDeviceBaseId, deviceBaseId);
        vehicleInfoLambdaUpdateWrapper.set(VehicleInfo::getUseCaseId, VehicleUseCaseEnum.TEST.getValue());
        vehicleInfoMapper.update(null, vehicleInfoLambdaUpdateWrapper);

        //更新维修单中保存的状态
        LambdaQueryWrapper<RequireInfo> requireInfoLambdaQueryWrapper = new LambdaQueryWrapper<>();
        requireInfoLambdaQueryWrapper.eq(RequireInfo::getVehicleId, deviceBaseId);
        List<Integer> list = Arrays.asList(RequireStatusEnum.TO_DO_ACCEPT.getValue(), RequireStatusEnum.ACCEPTED.getValue(), RequireStatusEnum.MAINTAINING.getValue(), RequireStatusEnum.TO_DO_CHECK.getValue());
        requireInfoLambdaQueryWrapper.in(RequireInfo::getStatus, list);
        List<RequireInfo> requireInfoList = requireInfoMapper.selectList(requireInfoLambdaQueryWrapper);
        if (requireInfoList != null && requireInfoList.size() > 0) {
            for (RequireInfo requireInfo : requireInfoList) {
                RequireInfo updateRequireInfo = new RequireInfo();
                updateRequireInfo.setId(requireInfo.getId());
                updateRequireInfo.setOldVehicleStage(VehicleStageEnum.SCHEDULE.getValue());
                updateRequireInfo.setOldHardwareStatus(VehicleHardwareStatusEnum.UNBIND.getValue());
                requireInfoMapper.updateById(updateRequireInfo);
            }
        }

        //同步通用设备管理平台-取消分组
        String productKey;
        String deviceName;
        //产品标识
        if (ProductTypeEnum.VEHICLE.getValue().equals(deviceBaseInfo.getProductType())) {
            productKey = productKeyProperties.getVehicle();
            deviceName = deviceBaseInfo.getName();
        } else if (ProductTypeEnum.ROBOT.getValue().equals(deviceBaseInfo.getProductType())) {
            productKey = productKeyProperties.getRobot();
            deviceName = deviceBaseInfo.getSerialNo();
        } else {
            productKey = productKeyProperties.getIntegrate();
            deviceName = deviceBaseInfo.getSerialNo();
        }
        intelligentDeviceServerDeviceManager.cancelIntelligentDeviceGroup(productKey, deviceName);


        //提交完成后发送消息
        if (TransactionSynchronizationManager.isSynchronizationActive()) {
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    //旧消息只发送无人车相关的
                    if (ProductTypeEnum.VEHICLE.getValue().equals(deviceBaseInfo.getProductType())) {
                        //发送变更站点的消息
                        List<String> vehicleNameList = new ArrayList<>();
                        vehicleNameList.add(deviceBaseInfo.getName());
                        log.info("车辆交付解绑车辆人车关系发生变化消息发送->车辆列表:{},旧站点:{},新站点:null", vehicleNameList, stationBaseInfo.getId());
                        jmqDeviceProducer.sendUserVehicleChangeMessage(vehicleNameList, stationBaseInfo.getId(), null);
                    }

                    //新版消息推送
                    log.info("车辆交付-解绑,发送消息变化,vehicleName:{}", deviceBaseInfo.getName());
                    jmqDeviceProducer.sendMetadataVehicleMessage(OperationTypeEnum.EDIT, deviceBaseInfo.getName());
                    //车点关系发生变化
                    List<StopVehicleDetailBO> oldVehicleStop = BeanCopyUtils.copyListProperties(oldVehicleStopBasicBOS, StopVehicleDetailBO::new);
                    jmqStopProducer.sendVehicleStopMessage(stationBaseInfo.getId(), deviceBaseInfo, oldVehicleStop, null, null, OperationTypeEnum.EDIT);
                }
            });
        }
        // 返回
        return BaseResponseEnum.SUCCESS;
    }

    /**
     * @param stationBaseId:
     * @param deviceBaseIdSet:
     * @return Map<Integer, VehiclePointCountBO>
     * @description 统计无人车设备绑定的停靠点数量
     * <AUTHOR>
     * @date 2024/3/9 19:58
     */
    private Map<Integer, VehiclePointCountBO> getVehicleStopCount(Integer stationBaseId, Set<Integer> deviceBaseIdSet) {
        if (Objects.isNull(stationBaseId)) {
            return new HashMap<>();
        }
        //查询停靠点
        QueryWrapper<VehicleStop> vehicleStopQueryWrapper = new QueryWrapper<>();
        vehicleStopQueryWrapper.in("vs.vehicle_id", deviceBaseIdSet);
        vehicleStopQueryWrapper.eq("vs.deleted", DELETED);
        List<VehicleStopInfoBO> vehicleStopInfoBOList = vehicleStopMapper.selectVehicleStop(vehicleStopQueryWrapper);
        if (CollectionUtils.isEmpty(vehicleStopInfoBOList)) {
            return new HashMap<>();
        }
        Map<Integer, VehiclePointCountBO> vehicleIdVehiclePointCountBOMap = new HashMap<>();
        Map<Integer, List<VehicleStopInfoBO>> vehicleIdVehicleStopInfoBOMap = vehicleStopInfoBOList.stream().collect(Collectors.groupingBy(VehicleStopInfoBO::getDeviceBaseId));
        vehicleIdVehicleStopInfoBOMap.forEach((deviceBaseId, vehicleStopInfoBOS) -> {
            VehiclePointCountBO vehiclePointCountBO = new VehiclePointCountBO();
            Integer jumpPointNumber = (int) vehicleStopInfoBOS.stream().filter(vehicleStopInfoBO -> !stationBaseId.equals(vehicleStopInfoBO.getStationBaseId())).count();
            Integer localPointNumber = (int) vehicleStopInfoBOS.stream().filter(vehicleStopInfoBO -> stationBaseId.equals(vehicleStopInfoBO.getStationBaseId())).count();
            vehiclePointCountBO.setJumpPointCount(jumpPointNumber);
            vehiclePointCountBO.setLocalPointCount(localPointNumber);
            vehiclePointCountBO.setAllPointCount(jumpPointNumber + localPointNumber);
            vehicleIdVehiclePointCountBOMap.put(deviceBaseId, vehiclePointCountBO);

        });
        return vehicleIdVehiclePointCountBOMap;
    }

    /**
     * @param stationBaseId:
     * @return List<StationVehicleListDTO>
     * @description 获取站点下的无人车信息列表
     * <AUTHOR>
     * @date 2024/3/12 14:51
     */
    public List<StationVehicleListDTO> getVehicleListOfStation(Integer stationBaseId) {
        LambdaQueryWrapper<DeviceBaseInfo> deviceBaseInfoLambdaQueryWrapper = new LambdaQueryWrapper<>();
        deviceBaseInfoLambdaQueryWrapper.eq(DeviceBaseInfo::getStationBaseId, stationBaseId);
        deviceBaseInfoLambdaQueryWrapper.select(DeviceBaseInfo::getName, DeviceBaseInfo::getId, DeviceBaseInfo::getSerialNo);
        List<DeviceBaseInfo> deviceBaseInfoList = deviceBaseInfoMapper.selectList(deviceBaseInfoLambdaQueryWrapper);
        List<StationVehicleListDTO> stationVehicleListDTOList = new ArrayList<>();
        deviceBaseInfoList.forEach(deviceBaseInfo -> {
            StationVehicleListDTO stationVehicleListDTO = new StationVehicleListDTO();
            stationVehicleListDTO.setDeviceBaseId(deviceBaseInfo.getId());
            stationVehicleListDTO.setDeviceName(deviceBaseInfo.getName());
            stationVehicleListDTO.setSerialNo(deviceBaseInfo.getSerialNo());
            stationVehicleListDTOList.add(stationVehicleListDTO);
        });
        return stationVehicleListDTOList;
    }

    /**
     * @param stationVehicleBindStopBatchVO:
     * @return BaseResponseEnum
     * @description 批量绑定停靠点
     * <AUTHOR>
     * @date 2024/3/13 15:35
     */
    @Transactional(rollbackFor = Exception.class)
    public BaseResponseEnum stationVehicleBindStopBatch(StationVehicleBindStopBatchVO stationVehicleBindStopBatchVO) {
        String username = JsfLoginUtil.getUsername();
        //判断站点状态不能是停用
        StationBaseInfo stationBaseInfo = stationBaseInfoMapper.selectById(stationVehicleBindStopBatchVO.getStationBaseId());
        if (stationBaseInfo.getEnable().equals(EnableEnum.DISABLE.getEnable())) {
            throw new AppRuntimeException("该站点已停用,请先启用站点再操作");
        }

        //获取所选车辆原有的停靠点
        QueryWrapper<VehicleStop> vehicleStopQueryWrapper = new QueryWrapper<>();
        vehicleStopQueryWrapper.in("vs.vehicle_id", stationVehicleBindStopBatchVO.getDeviceBaseIdList());
        vehicleStopQueryWrapper.eq("vs.deleted", DELETED);
        List<VehicleStopInfoBO> allDeviceBindStopInfoList = vehicleStopMapper.selectVehicleStop(vehicleStopQueryWrapper);

        //查询车辆信息
        LambdaQueryWrapper<DeviceBaseInfo> deviceBaseInfoLambdaQueryWrapper = new LambdaQueryWrapper<>();
        deviceBaseInfoLambdaQueryWrapper.in(DeviceBaseInfo::getId, stationVehicleBindStopBatchVO.getDeviceBaseIdList());
        deviceBaseInfoLambdaQueryWrapper.select(DeviceBaseInfo::getId, DeviceBaseInfo::getStationBaseId, DeviceBaseInfo::getName, DeviceBaseInfo::getProductType);
        List<DeviceBaseInfo> deviceBaseInfoList = deviceBaseInfoMapper.selectList(deviceBaseInfoLambdaQueryWrapper);
        Map<Integer, DeviceBaseInfo> deviceBaseIdInfoMap = deviceBaseInfoList.stream().collect(Collectors.toMap(DeviceBaseInfo::getId, DeviceBaseInfo -> DeviceBaseInfo));

        //判断所选设备没有绑定本站的home点
        List<VehicleStopInfoBO> linkedLocalStationHomeStopDeviceList = allDeviceBindStopInfoList.stream().
                filter(vehicleStopInfoBO -> vehicleStopInfoBO.getStopType().equals(StopTypeEnum.HOME.getValue()) && vehicleStopInfoBO.getStationBaseId().equals(stationBaseInfo.getId()))
                .collect(Collectors.toList());
        if (Objects.nonNull(stationVehicleBindStopBatchVO.getHomeStopId()) && !CollectionUtils.isEmpty(linkedLocalStationHomeStopDeviceList)) {
            Set<String> linkedLocalStationHomeStopDeviceNameSet = linkedLocalStationHomeStopDeviceList.stream().map(VehicleStopInfoBO::getDeviceName).collect(Collectors.toSet());
            throw new AppRuntimeException("车牌号{" + linkedLocalStationHomeStopDeviceNameSet + "}存在home点,不允许批量");
        }

        //将增量的停靠点插入
        List<Integer> linkedStopList = stationVehicleBindStopBatchVO.getLinkedStopList();
        linkedStopList.add(stationVehicleBindStopBatchVO.getHomeStopId());
        if (CollectionUtils.isEmpty(linkedStopList)) {
            throw new AppRuntimeException(BaseResponseEnum.ERROR_BIND_STOP_EMPTY);
        }

        //需要插入的停靠点id列表
        Set<Integer> allNeedInsertStopIdSet = new HashSet<>();

        //每辆车需要插入的停靠点id列表
        Map<Integer, Set<Integer>> deviceBaseIdStopIdSetMap = new HashMap<>();

        //旧站点按车分组,发kafka用
        Map<Integer, List<VehicleStopInfoBO>> deviceBaseIdVehicleStopInfoListMap = allDeviceBindStopInfoList.stream().collect(Collectors.groupingBy(VehicleStopInfoBO::getDeviceBaseId));
        stationVehicleBindStopBatchVO.getDeviceBaseIdList().forEach(deviceBaseId -> {
            if (!CollectionUtils.isEmpty(deviceBaseIdVehicleStopInfoListMap.get(deviceBaseId))) {
                Set<Integer> bindStopIdSet = deviceBaseIdVehicleStopInfoListMap.get(deviceBaseId).stream().map(VehicleStopInfoBO::getStopId).collect(Collectors.toSet());
                Set<Integer> needBindStopIdSet = linkedStopList.stream().filter(notBindStopId -> !bindStopIdSet.contains(notBindStopId)).collect(Collectors.toSet());
                if (!CollectionUtils.isEmpty(needBindStopIdSet)) {
                    allNeedInsertStopIdSet.addAll(needBindStopIdSet);
                    deviceBaseIdStopIdSetMap.put(deviceBaseId, needBindStopIdSet);
                }
            }else {
                allNeedInsertStopIdSet.addAll(linkedStopList);
                deviceBaseIdStopIdSetMap.put(deviceBaseId, new HashSet<>(linkedStopList));
            }
        });

        if (!CollectionUtils.isEmpty(allNeedInsertStopIdSet)) {
            //查询停靠点的等待时间
            LambdaQueryWrapper<Stop> stopLambdaQueryWrapper = new LambdaQueryWrapper<>();
            stopLambdaQueryWrapper.in(Stop::getId, allNeedInsertStopIdSet);
            stopLambdaQueryWrapper.select(Stop::getId, Stop::getWaitingTime);
            List<Stop> stopList = stopMapper.selectList(stopLambdaQueryWrapper);
            Map<Integer, Integer> stopIdWaitingTimeMap = stopList.stream().collect(Collectors.toMap(Stop::getId, Stop::getWaitingTime));

            //插入
            deviceBaseIdStopIdSetMap.forEach((deviceBaseId, needInsertStopIdSet) -> {
                needInsertStopIdSet.forEach(needInsertStopId -> {
                    if (Objects.nonNull(needInsertStopId)) {
                        VehicleStop vehicleStop = new VehicleStop();
                        vehicleStop.setVehicleId(deviceBaseId);
                        vehicleStop.setStopId(needInsertStopId);
                        vehicleStop.setWaitingTime(stopIdWaitingTimeMap.get(needInsertStopId));
                        vehicleStop.setCreateUser(username);
                        vehicleStop.setModifyUser(username);
                        vehicleStopMapper.insert(vehicleStop);
                    }
                });
            });
        }

        //提交完成后发送消息
        if (TransactionSynchronizationManager.isSynchronizationActive()) {
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    deviceBaseIdStopIdSetMap.forEach((deviceBaseId, needInsertStopIdSet) -> {
                        DeviceBaseInfo deviceBaseInfo = deviceBaseIdInfoMap.get(deviceBaseId);
                        //v2
                        //查询当前车辆绑定的全量的停靠点信息
                        QueryWrapper<VehicleStop> nowVehicleStopQueryWrapper = new QueryWrapper<>();
                        nowVehicleStopQueryWrapper.eq("vs.vehicle_id", deviceBaseId);
                        nowVehicleStopQueryWrapper.eq("vs.deleted", DELETED);
                        List<VehicleStopInfoBO> nowVehicleStopInfoList = vehicleStopMapper.selectVehicleStop(nowVehicleStopQueryWrapper);

                        List<StopVehicleDetailBO> oldStopVehicleDetailBOList = new ArrayList<>();

                        List<VehicleStopInfoBO> vehicleStopInfoBOList = deviceBaseIdVehicleStopInfoListMap.get(deviceBaseId);
                        if (!CollectionUtils.isEmpty(vehicleStopInfoBOList)) {
                            vehicleStopInfoBOList.forEach(oldVehicleStopInfoBO -> {
                                StopVehicleDetailBO stopVehicleDetailBO = new StopVehicleDetailBO();
                                stopVehicleDetailBO.setStopId(oldVehicleStopInfoBO.getStopId());
                                stopVehicleDetailBO.setStationBaseId(oldVehicleStopInfoBO.getStationBaseId());
                                stopVehicleDetailBO.setWaitingTime(oldVehicleStopInfoBO.getWaitingTime());
                                stopVehicleDetailBO.setDeviceBaseId(oldVehicleStopInfoBO.getDeviceBaseId());
                                stopVehicleDetailBO.setDeviceName(oldVehicleStopInfoBO.getDeviceName());
                                oldStopVehicleDetailBOList.add(stopVehicleDetailBO);
                            });
                        }

                        List<StopVehicleDetailBO> nowStopVehicleDetailBOList = new ArrayList<>();
                        nowVehicleStopInfoList.forEach(nowVehicleStopInfoBO -> {
                            StopVehicleDetailBO stopVehicleDetailBO = new StopVehicleDetailBO();
                            stopVehicleDetailBO.setStopId(nowVehicleStopInfoBO.getStopId());
                            stopVehicleDetailBO.setStationBaseId(nowVehicleStopInfoBO.getStationBaseId());
                            stopVehicleDetailBO.setWaitingTime(nowVehicleStopInfoBO.getWaitingTime());
                            stopVehicleDetailBO.setDeviceBaseId(nowVehicleStopInfoBO.getDeviceBaseId());
                            stopVehicleDetailBO.setDeviceName(nowVehicleStopInfoBO.getDeviceName());
                            nowStopVehicleDetailBOList.add(stopVehicleDetailBO);
                        });
                        jmqStopProducer.sendVehicleStopMessage(stationBaseInfo.getId(), deviceBaseInfo, oldStopVehicleDetailBOList, null, nowStopVehicleDetailBOList, OperationTypeEnum.EDIT);
                    });
                }
            });
        }

        return BaseResponseEnum.SUCCESS;
    }
}
