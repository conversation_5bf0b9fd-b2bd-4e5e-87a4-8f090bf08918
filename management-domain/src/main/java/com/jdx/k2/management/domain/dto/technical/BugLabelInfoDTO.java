package com.jdx.k2.management.domain.dto.technical;

import lombok.Data;

/**
 * 问题标签详情传输对象
 */
@Data
public class BugLabelInfoDTO {

    /**
     * 问题标签ID
     */
    private Integer bugLabelId;

    /**
     * 问题标签名称
     */
    private String bugLabelName;

    /**
     * 问题分类ID
     */
    private Integer bugCategoryId;

    /**
     * 模块对应code
     */
    private String moduleCode;

    /**
     * 模块名称
     */
    private String moduleName;
}
