/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.k2.management.domain.vo.cockpit;

import java.util.List;
import javax.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 驾驶团队位置信息请求对象
 */
@Data
public class CockpitTeamPositionVO {

    /**
     * 驾驶团队编号
     */
    @NotBlank(message = "驾驶团队编号不能为空")
    private String cockpitTeamNumber;

    /**
     * 位置级别
     */
    @NotBlank(message = "位置级别不能为空")
    private String level;

    /**
     * 城市id列表
     */
    private List<Integer> cityIdList;

    /**
     * 站点id列表
     */
    private List<Integer> stationBaseIdList;

    /**
     * 车牌号列表
     */
    private List<String> deviceNameList;
}
