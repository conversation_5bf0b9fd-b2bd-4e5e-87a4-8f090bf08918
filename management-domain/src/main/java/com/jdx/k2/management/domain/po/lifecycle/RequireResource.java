package com.jdx.k2.management.domain.po.lifecycle;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jdx.k2.management.domain.base.BaseDomain;
import lombok.Data;

/**
 * 车辆维修上报资源表
 * <AUTHOR>
 */
@Data
@TableName("require_resource")
public class RequireResource extends BaseDomain {

    /**
     * 车辆维修单编号
     */
    private String requireNumber;

    /**
     * 资源类型
     */
    private String type;

    /**
     * 资源标识
     */
    private String resourceKey;
}
