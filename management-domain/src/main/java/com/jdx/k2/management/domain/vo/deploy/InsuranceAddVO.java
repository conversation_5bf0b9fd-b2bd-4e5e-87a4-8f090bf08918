package com.jdx.k2.management.domain.vo.deploy;

import com.jdx.k2.management.domain.vo.common.AttachmentVO;
import lombok.Data;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 新建保单请求实体
 * @date 2025年03月04日
 * @version: 1.0
 */
@Data
public class InsuranceAddVO {


    /**
     * 保单号
     */
    @NotBlank(message = "保单号不能为空")
    private String policyNumber;

    /**
     * 投保公司
     */
    @NotBlank(message = "投保公司不能为空")
    private String insuranceCompany;

    /**
     * 投保类型
     */
    @NotBlank(message = "投保类型不能为空")
    private String insuranceType;

    /**
     * 投保公司主体
     */
    @NotBlank(message = "投保公司主体不能为空")
    private String insuredEntity;

    /**
     * 投保金额
     */
    @NotNull(message = "投保金额不能为空")
    private BigDecimal policyCoverageAmount;

    /**
     * 保单生效日期(yyyy-MM-dd)
     */
    @NotNull(message = "保单生效时间不能为空")
    private String effectiveStartTime;

    /**
     * 保单失效日期(yyyy-MM-dd)
     */
    @NotNull(message = "保单失效时间不能为空")
    private String effectiveEndTime;

    /**
     * 保单附件信息
     */
    @NotNull(message = "保单附件信息不能为空")
    private AttachmentVO policyAttachment;

    /**
     * 其他附件信息
     */
    private List<AttachmentVO> otherAttachmentList;

    /**
     * 投保车辆信息
     */
    @Valid
    @NotEmpty(message = "投保车辆不能为空")
    private List<InsuranceDeviceAddVO> deviceInsuranceInfoList;

    /**
     * 备注
     */
    private String remark;
}
