package com.jdx.k2.management.domain.vo.station;

import lombok.Data;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 批量绑定停靠点
 * @date 2024年03月13日
 * @version: 1.0
 */
@Data
public class StationVehicleBindStopBatchVO {

    /**
     * 站点基础信息表id
     */
    @NotNull(message = "站点id不能为空")
    private Integer stationBaseId;

    /**
     * 车辆ID标识
     */
    @NotEmpty(message = "设备列表不能为空")
    private List<Integer> deviceBaseIdList;

    /**
     * 关联点位
     */
    private Integer homeStopId;

    /**
     * 跨站停靠点
     */
    private List<Integer> linkedStopList;
}
