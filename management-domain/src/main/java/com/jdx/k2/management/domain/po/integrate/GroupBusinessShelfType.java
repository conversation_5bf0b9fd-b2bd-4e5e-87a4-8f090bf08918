package com.jdx.k2.management.domain.po.integrate;

import com.jdx.k2.management.domain.base.BaseDomain;
import lombok.Data;

/**
 * <AUTHOR>
 * @description: 多合一站点业务与上装类型关系表
 * @date 2025年04月03日
 * @version: 1.0
 */
@Data
public class GroupBusinessShelfType extends BaseDomain {

    /**
     * group_business表id
     */
    private Integer groupBusinessId;

    /**
     * shelf_type表id
     */
    private Integer shelfTypeId;
}
