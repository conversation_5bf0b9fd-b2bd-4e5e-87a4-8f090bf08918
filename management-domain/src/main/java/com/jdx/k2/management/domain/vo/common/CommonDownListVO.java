/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.k2.management.domain.vo.common;

import java.util.List;
import javax.validation.constraints.NotEmpty;
import lombok.Data;

/**
 * 运营管理系统公共模块下拉列表请求对象
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class CommonDownListVO {

    /**
     * 下拉列表关键字列表
     */
    @NotEmpty(message = "下拉列表关键字列表不能为空")
    private List<String> keyList;
}
