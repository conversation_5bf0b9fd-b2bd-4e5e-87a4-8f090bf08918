package com.jdx.k2.management.domain.vo.cockpit;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 驾驶舱列表请求实体
 */
@Data
public class CockpitListVO {

    /**
     * 驾驶团队编号
     */
    @NotBlank(message = "驾驶团队编号不能为空")
    private String cockpitTeamNumber;

    /**
     * 车牌号
     */
    private String vehicleName;

    /**
     * 坐席号
     */
    private String cockpitNumber;
}
