/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.k2.management.domain.vo.device;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * 车辆管理系统变更车型状态请求对象
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class VehicleTypeEnableVO {

    /**
     * 车型编号
     */
    @NotNull(message = "车型编号不能为空")
    private Integer id;

    /**
     * 是否生效
     */
    @NotNull(message = "是否生效不能为空")
    @Min(value = 0, message = "是否生效最小值为0")
    @Max(value = 1, message = "是否生效最大值为1")
    private Integer enable;
}