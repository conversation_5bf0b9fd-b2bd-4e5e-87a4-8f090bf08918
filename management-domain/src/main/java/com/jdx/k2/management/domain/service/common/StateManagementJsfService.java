package com.jdx.k2.management.domain.service.common;

import com.jdx.k2.management.domain.po.common.State;
import com.jdx.k2.management.domain.po.common.City;
import com.jdx.k2.management.domain.vo.common.CityOfStateGetVO;
import com.jdx.rover.common.utils.result.HttpResult;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 省份管理模块接口API
 */
public interface StateManagementJsfService {

    /**
     * 获取所有省份列表
     * @return List<State>
     */
    HttpResult<List<State>> getStateList();

    /**
     * 获取省份下的所有市
     * @return List<City>
     */
    HttpResult<List<City>> getCityOfState(@Valid @NotNull(message = "省份ID不能为空") CityOfStateGetVO cityOfStateGetVO);
}