package com.jdx.k2.management.domain.vo.other;

import java.io.Serializable;
import lombok.Data;
import javax.validation.constraints.Min;

/**
 * 权限管理-资源管理-列表查询请求对象
 *
 * <AUTHOR>
 */
@Data
public class PermissionResourceListVO implements Serializable {

    private static final long serialVersionUID = 3902792647767544271L;

    /**
     * 资源名称 模糊查询
     */
    private String name;

    /**
     * 应用系统
     */
    private Integer app;

    /**
     * 资源编号 模糊查询
     */
    private String resourceCode;

    /**
     * 当前页码
     */
    @Min(value = 1L, message = "最小值为1")
    private  Integer pageNum;

    /**
     * 每页的数量
     */
    @Min(value = 1L, message = "最小值为1")
    private  Integer pageSize;
}
