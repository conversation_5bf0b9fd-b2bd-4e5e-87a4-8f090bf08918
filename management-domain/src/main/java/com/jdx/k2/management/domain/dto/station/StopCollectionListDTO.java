/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.k2.management.domain.dto.station;

import lombok.Data;

/**
 * 运营管理系统代收点管理代收点列表数据传输对象
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class StopCollectionListDTO {

    /**
     * 代收点ID标识
     */
    private Integer id;

    /**
     * 代收点名称
     */
    private String name;

    /**
     * 所属停靠点
     */
    private String stopName;

    /**
     * 所属站点
     */
    private String stationName;

    /**
     * 代收点状态ID标识
     */
    private Integer enable;

    /**
     * 代收点状态
     */
    private String enableName;

    /**
     * 代收人数量
     */
    private Integer stopCollectionUserNumber;

    /**
     * 默认代收人
     */
    private String defaultStopCollectionUserName;
}
