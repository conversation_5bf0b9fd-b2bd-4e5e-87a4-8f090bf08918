/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.k2.management.domain.dto.station;

import java.util.List;
import com.jdx.k2.management.domain.vo.lifecycle.VehicleLinkPointVO;
import lombok.Data;

/**
 * 运营管理系统车辆管理在用车辆详情数据传输对象
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class StationVehicleDetailDTO {

    /**
     * 所在城市
     */
    private Integer cityId;

    /**
     * 站点
     */
    private Integer stationBaseId;

    /**
     * 站点名称
     */
    private String stationName;

    /**
     * 车辆ID标识
     */
    private Integer deviceBaseId;

    /**
     * 设备车牌号
     */
    private String deviceName;

    /**
     * 设备车架号
     */
    private String serialNo;

    /**
     * 产品类型
     */
    private String productType;

    /**
     * 产品类型名称
     */
    private String productTypeName;

    /**
     * 设备类型基础信息表id
     */
    private Integer deviceTypeBaseId;

    /**
     * 设备类型名称
     */
    private String deviceTypeName;

    /**
     * 车辆业务类型
     */
    private String businessType;

    /**
     * 车辆业务类型名称
     */
    private String businessTypeName;


    /**
     * 车辆箱体
     */
    private Integer boxTemplateId;

    /**
     * 车辆箱体名称
     */
    private String boxTemplateName;

    /**
     * 设备货箱编号
     */
    private Integer boxId;

    /**
     * 格口信息
     */
    private List<VehicleBoxGridDTO> gridList;

    /**
     * 无人车在青龙系统中的编号
     */
    private String vCode;

    /**
     * 关联点位
     */
    private List<VehicleLinkPointDTO> linkPointList;

    /**
     * 跨站停靠点信息
     */
    private List<JumpLinkStationStopDTO> jumpStationStopList;
}