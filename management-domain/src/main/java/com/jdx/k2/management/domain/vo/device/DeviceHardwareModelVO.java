package com.jdx.k2.management.domain.vo.device;

import lombok.Data;

/**
 * @descrition: 设备硬件型号传输对象
 * @author: do<PERSON><PERSON><PERSON>
 * @date: 2024/3/13 16:08
 */

@Data
public class DeviceHardwareModelVO {

    /**
     * 来源：传感器方案，车型，车辆
     */
    private String source;

    /**
     * 硬件型号id
     */
    private Integer hardwareModelId;

    /**
     * 硬件序列号
     */
    private String hardwareNumber;

    /**
     * 硬件数量
     */
    private Integer num;
}
