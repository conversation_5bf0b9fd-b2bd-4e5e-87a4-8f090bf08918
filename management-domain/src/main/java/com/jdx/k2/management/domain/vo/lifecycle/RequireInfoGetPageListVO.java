package com.jdx.k2.management.domain.vo.lifecycle;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import java.util.List;
import lombok.Data;

import javax.validation.constraints.Min;

/**
 * 维修单查询实体
 */
@Data
public class RequireInfoGetPageListVO {

    /**
     * 维修单号
     */
    private String number;

    /**
     * 车牌号
     */
    private String deviceName;

    /**
     * 联系人
     */
    private String reportErp;

    /**
     * 提报开始时间
     */
    private Date reportStartTime;

    /**
     * 提报结束时间
     */
    private Date reportEndTime;

    /**
     * 维修单状态
     */
    private List<Integer> status;

    /**
     * 是否影响运营
     */
    private Integer isInfluenceOperation;

    /**
     * 站点Id
     */
    private String stationBaseId;


    /**
     * 当前页码
     */
    @Min(value = 1L, message = "最小值为1")
    private  Integer pageNum;

    /**
     * 每页的数量
     */
    @Min(value = 1L, message = "最小值为1")
    private  Integer pageSize;

}
