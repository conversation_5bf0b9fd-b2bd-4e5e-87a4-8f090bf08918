package com.jdx.k2.management.domain.dto.warehouse;

import lombok.Data;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 车型上装类型返回实体
 * @date 2025年04月08日
 * @version: 1.0
 */
@Data
public class DeviceTypeShelfTypeDTO {


    /**
     * 设备类型id
     */
    private Integer deviceTypeBaseId;

    /**
     * 设备类型名称
     */
    private String deviceTypeName;

    /**
     * 上装类型信息
     */
    private List<ShelfTypeBriefDTO> shelfTypeList;

}
