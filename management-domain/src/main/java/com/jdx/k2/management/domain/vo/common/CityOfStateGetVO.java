package com.jdx.k2.management.domain.vo.common;

import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * @Description:
 * @Author: chenxiangrui7
 * @Date: 2025/2/18 17:45
 */
@Data
public class CityOfStateGetVO {

    /**
     * stateId
     */
    @NotNull(message = "stateId不能为空")
    private Integer stateId;
}
