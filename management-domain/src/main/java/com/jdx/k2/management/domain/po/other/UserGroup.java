package com.jdx.k2.management.domain.po.other;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jdx.k2.management.domain.base.BaseDomain;
import lombok.Data;

/**
 * 用户关联分组 数据库表名为user_group
 *
 * <AUTHOR>
 */
@Data
@TableName("user_group")
public class UserGroup extends BaseDomain {

    /**
     * 用户编号
     */
    private String userName;

    /**
     * 分组编号
     */
    private String groupNumber;
}