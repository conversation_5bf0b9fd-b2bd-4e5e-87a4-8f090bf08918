package com.jdx.k2.management.domain.vo.station.warehouse;

import lombok.Data;
import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @description: 多合一上装分页列表请求实体
 * @date 2025年04月07日
 * @version: 1.0
 */
@Data
public class WarehouseShelfPageGetVO {

    /**
     * 分页编号
     */
    @NotNull(message = "当前页码不能为空")
    @Min(value = 1L, message = "最小值为1")
    private Integer pageNum;

    /**
     * 分页大小
     */
    @NotNull(message = "每页数量不能为空")
    @Min(value = 1L, message = "最小值为1")
    private Integer pageSize;

    /**
     * 站点id
     */
    @NotNull(message = "站点id不能为空")
    private Integer stationBaseId;
}
