package com.jdx.k2.management.domain.po.device;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jdx.k2.management.domain.base.BaseDomain;
import lombok.Data;

/**
 * 传感器方案详情
 * <AUTHOR>
 */
@Data
@TableName("sensor_scheme_detail")
public class SensorSchemeDetail extends BaseDomain {


    /**
     * 传感器方案Id
     */
    private Integer sensorSchemeId;

    /**
     * 硬件类型Id
     */
    private Integer hardwareTypeId;

    /**
     * 硬件类型用途Id
     */
    private Integer hardwareTypeUsageId;

    /**
     * 硬件型号Id
     */
    private Integer hardwareModelId;

    /**
     * 使用数量
     */
    private Integer useNumber;
}
