/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.k2.management.domain.po.technical;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jdx.k2.management.domain.base.BaseDomain;
import lombok.Data;

import java.util.Date;

/**
 * 缺陷状态快照表
 *
 * <AUTHOR>
 */
@Data
@TableName("bug_snap_info")
public class BugSnapInfo extends BaseDomain {

    /**
     * 记录日期
     */
    private Date recordDate;

    /**
     * 缺陷编号
     */
    private String bugCode;

    /**
     * 缺陷状态
     */
    private String bugStatus;

    /**
     * 亮灯颜色
     */
    private String alarmColor;
}
