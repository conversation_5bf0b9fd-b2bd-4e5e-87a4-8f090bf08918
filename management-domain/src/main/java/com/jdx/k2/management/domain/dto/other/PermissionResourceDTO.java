package com.jdx.k2.management.domain.dto.other;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import com.jd.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * 权限管理-资源管理-列表详情返回对象
 *
 * <AUTHOR>
 */
@Data
public class PermissionResourceDTO implements Serializable {

    private static final long serialVersionUID = 4884744689666857496L;

    /**
     * 资源编号
     */
    private String number;

    /**
     * 父级资源编号
     */
    private String parentNumber;

    /**
     * 名称
     */
    private String name;

    /**
     * 描述
     */
    private String description;

    /**
     * 应用系统
     */
    private Integer app;

    /**
     * 资源类型
     */
    private Integer type;

    /**
     * 资源标识
     */
    private String resourceCode;

    /**
     * 路径
     */
    private String path;

    /**
     * 方法(POST/GET/DELETE/PUT/PATCH)
     */
    private String method;

    /**
     * 菜单顺序
     */
    private Integer orderBy;

    /**
     * 更新时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date modifyTime;

    /**
     * 应用系统名称
     */
    private String appName;

    /**
     * 资源类型名称
     */
    private String typeName;

    /**
     * 是否有子资源
     */
    private Boolean hasChildren;

    /**
     * 子资源
     */
    private List<PermissionResourceDTO> children;
}
