package com.jdx.k2.management.domain.base;

import com.jdx.k2.management.domain.constant.TraceConstant;
import com.jdx.rover.common.utils.enums.HttpCodeEnum;
import com.jdx.rover.common.utils.result.HttpResult;
import lombok.Data;
import org.slf4j.MDC;

/**
 * <p>
 *
 * @description: TODO 类描述
 * </p>
 * @author: linbingbing6
 * @date: 2024/3/28
 **/
@Data
public class AppResult<T> extends HttpResult<T> {

    /**
     * traceId
     */
    private String traceId;

    public AppResult() {
        this.traceId = MDC.get(TraceConstant.TRACE_ID);
    }

    /**
     * @param code 返回编码
     * @param data 返回数据
     * @return
     */
    public AppResult(String code, T data) {
        super(code, data);
        this.traceId = MDC.get(TraceConstant.TRACE_ID);
    }

    /**
     * @param httpCode 返回编码
     * @param data     返回数据
     * @return
     */
    public AppResult(HttpCodeEnum httpCode, T data) {
        super(httpCode, data);
        this.traceId = MDC.get(TraceConstant.TRACE_ID);
    }

    /**
     * @param code    编码
     * @param message 描述信息
     * @param data    返回数据
     * @return
     */
    public AppResult(String code, String message, T data) {
        super(code, message, data);
        this.traceId = MDC.get(TraceConstant.TRACE_ID);
    }


}
