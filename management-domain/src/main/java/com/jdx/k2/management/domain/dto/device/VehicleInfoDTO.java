/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.k2.management.domain.dto.device;

import java.util.Date;
import java.util.List;
import com.jd.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * 车辆信息管理数据传输对象
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class VehicleInfoDTO {

    /**
     * 编号
     */
    private Integer id;

    /**
     * 车牌
     */
    private String name;

    /**
     * 车架号
     */
    private String serialNo;

    /**
     * 所属产品
     */
    private String productType;

    /**
     * 所属产品名称
     */
    private String productTypeName;

    /**
     * 车辆业务类型
     */
    private String businessType;

    /**
     * 车辆业务类型名称
     */
    private String businessTypeName;

    /**
     * 系统标签
     */
    private List<Integer> tagList;

    /**
     * 系统标签名称
     */
    private String tagName;

    /**
     * 箱体模板编号
     */
    private Integer boxTemplateId;

    /**
     * 箱体格口模板名称
     */
    private String boxTemplateName;

    /**
     * 箱体名称
     */
    private String hardwareModelName;

    /**
     * 箱体型号
     */
    private String hardwareModelModel;

    /**
     * 格口数
     */
    private Integer gridNum;

    /**
     * 车型编号
     */
    private Integer deviceTypeBaseId;

    /**
     * 车型名称
     */
    private String deviceTypeName;

    /**
     * 车辆生命周期
     */
    private String hardwareStatusName;

    /**
     * 标定检测状态
     */
    private String checkStatusName;

    /**
     * 安卓设备Id
     */
    private String androidDeviceId;

    /**
     * 最近修改人
     */
    private String modifyUser;

    /**
     * 更新时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date modifyTime;

    /**
     * 供应商
     */
    private String supplier;

    /**
     * 供应商名称
     */
    private String supplierName;

}
