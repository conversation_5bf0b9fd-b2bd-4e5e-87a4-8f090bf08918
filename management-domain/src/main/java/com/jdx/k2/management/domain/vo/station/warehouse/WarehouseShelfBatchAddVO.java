package com.jdx.k2.management.domain.vo.station.warehouse;

import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @description: 批量新建上装请求实体
 * @date 2025年04月07日
 * @version: 1.0
 */
@Data
public class WarehouseShelfBatchAddVO {


    /**
     * 站点基础信息表id
     */
    @NotNull(message = "站点基础信息表id不能为空")
    private Integer stationBaseId;

    /**
     * 上装类型id
     */
    @NotNull(message = "上装类型id不能为空")
    private Integer shelfTypeId;

    /**
     * 固定号段
     */
    @NotBlank(message = "固定号段不能为空")
    private String prefixNo;

    /**
     * 起始号段
     */
    @NotNull(message = "起始号段不能为空")
    private Integer suffixStartNo;

    /**
     * 结束号段
     */
    @NotNull(message = "结束号段不能为空")
    private Integer suffixEndNo;

}
