package com.jdx.k2.management.domain.vo.issue;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 指标编辑传输对象
 */
@Data
public class IssueIndicatorEditVO {

    /**
     * 指标层编号
     */
    @NotBlank(message = "指标层编号不能为空")
    private String layerNumber;

    /**
     * 指标信息列表
     */
    @NotEmpty(message = "指标信息列表不能为空")
    @Valid
    private List<IssueIndicatorVO> indicatorInfoList;
}
