package com.jdx.k2.management.domain.po.technical;

import com.jdx.k2.management.domain.base.BaseDomain;
import lombok.Data;

import java.util.Date;

/**
 * 缺陷基础信息表
 */
@Data
public class BugInfo extends BaseDomain {

    /**
     * 缺陷编号
     */
    private String bugCode;

    /**
     * 缺陷id
     */
    private Long bugId;

    /**
     * 缺陷标题
     */
    private String bugTitle;

    /**
     * 缺陷优先级
     */
    private String bugPriority;

    /**
     * 缺陷严重程度
     */
    private String bugSeverity;

    /**
     * 缺陷提交人
     */
    private String bugProposer;

    /**
     * 缺陷受理人
     */
    private String bugAssignee;

    /**
     * 缺陷状态
     */
    private String bugStatus;

    /**
     * 缺陷创建时间
     */
    private Date bugCreateTime;

    /**
     * 预计修复完成时间
     */
    private Date bugEstimatedFixTime;

    /**
     * 车辆rover版本号
     */
    private String roverVersion;

    /**
     * 排查结论
     */
    private String bugConclusion;

    /**
     * 缺陷描述
     */
    private String bugDescription;

    /**
     * 解决方案
     */
    private String bugResolution;

    /**
     * 修复缺陷mrId
     */
    private String fixMrIds;

    /**
     * 关联缺陷
     */
    private String associatedBug;

    /**
     * 标签id
     */
    private Integer labelId;

    /**
     * QA跟进人
     */
    private String qaPerson;

    /**
     * 亮灯天数
     */
    private Integer alarmDays;

    /**
     * 研发解决天数
     */
    private Integer developerResolutionDays;

    /**
     * 亮灯颜色
     */
    private String alarmColor;

    /**
     * 用户体验功能局限标签
     */
    private String ueLimitTag;

    /**
     * 功能场景
     */
    private String functionScene;

    /**
     * 障碍物信息
     */
    private String obstacleInfo;

    /**
     * 障碍物方位
     */
    private String obstaclePosition;

    /**
     * 障碍物意图
     */
    private String obstacleIntent;

    /**
     * 道路结构
     */
    private String roadStructure;

    /**
     * 车型id
     */
    private Integer deviceTypeBaseId;

    /**
     * 车型名称
     */
    private String deviceTypeName;
}
