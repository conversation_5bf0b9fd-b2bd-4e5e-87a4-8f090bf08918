/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.k2.management.domain.dto.lifecycle;

import lombok.Data;

/**
 * 运营管理系统车辆管理调度池车辆列表数据传输对象
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class PoolVehicleListDTO {

    /**
     * 车辆id
     */
    private Integer id;

    /**
     * 车牌号
     */
    private String deviceName;

    /**
     * 车架号
     */
    private String serialNo;

    /**
     * 车辆类型
     */
    private String businessType;

    /**
     * 车辆类型名称
     */
    private String businessTypeName;

    /**
     * 产品类型
     */
    private String productType;

    /**
     * 产品类型名称
     */
    private String productTypeName;

    /**
     * 站点编号
     */
    private Integer stationBaseId;

    /**
     * 站点名称
     */
    private String stationName;

    /**
     * 站点用途
     */
    private String stationUseCase;

    /**
     * 站点用途名称
     */
    private String stationUseCaseName;

    /**
     * 车辆归属方
     */
    private String ownerUseCase;

    /**
     * 车辆归属方名称
     */
    private String ownerUseCaseName;

    /**
     * 车辆生命周期
     */
    private String hardwareStatus;

    /**
     * 车辆生命周期名称
     */
    private String hardwareStatusName;

    /**
     * 标定检测状态
     */
    private String checkStatus;

    /**
     * 标定检测状态名称
     */
    private String checkStatusName;

    /**
     * 是否存在维修单
     */
    private Integer isRequire;

    /**
     * 是否存在维修单
     */
    private String isRequireName;
}