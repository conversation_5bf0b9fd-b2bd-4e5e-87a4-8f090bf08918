package com.jdx.k2.management.domain.vo.task;

import lombok.Data;

import java.util.List;

/**
 * @description: 多合一任务列表VO
 * @author: do<PERSON><PERSON><PERSON>
 * @date: 2024/6/4
 */
@Data
public class WarehouseTaskPageListVO {

    /**
     * 站点id
     */
    private Integer stationBaseId;

    /**
     * 任务类型
     */
    private List<String> taskType;

    /**
     * 设备车牌号
     */
    private String deviceName;

    /**
     * 运输单id
     */
    private String orderNo;

    /**
     * 任务状态列表
     */
    private List<String> taskStatus;
}
