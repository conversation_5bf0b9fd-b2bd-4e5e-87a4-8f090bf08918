/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.k2.management.domain.vo.device;

import lombok.Data;
import javax.validation.constraints.Min;

/**

 * 箱体模板搜索请求对象

 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class BoxTemplateGetPageListVO {

    /**

     * 模板名称

     */
    private String name;

    /**

     * 箱体名称

     */
    private String hardwareModelName;

    /**

     * 格口数

     */
    private Integer gridNum;

    /**

     * 箱体型号

     */
    private String hardwareModelModel;

    /**

     * 模板状态

     */
    private Integer enable;

    /**
     * 所属产品
     */
    private String productType;

    /**
     * 当前页码
     */
    @Min(value = 1L, message = "最小值为1")
    private  Integer pageNum;

    /**
     * 每页的数量
     */
    @Min(value = 1L, message = "最小值为1")
    private  Integer pageSize;
}
