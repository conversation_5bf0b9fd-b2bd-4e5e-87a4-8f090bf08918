/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/
package com.jdx.k2.management.domain.vo.lifecycle;

import java.util.List;
import javax.validation.constraints.NotEmpty;
import lombok.Data;

/**
 * 运营管理系统车辆管理--批量绑定停靠点请求对象
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class VehicleBatchBindStopVO {

    /**
     * 车牌号列表
     */
    @NotEmpty(message = "所选车辆不能为空")
    private List<String> vehicleNameList;

    /**
     * 停靠点列表
     */
    private StopIdListVO stopList;
}
