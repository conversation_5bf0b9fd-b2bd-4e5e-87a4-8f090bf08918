package com.jdx.k2.management.domain.bo.device;

import lombok.Data;
import java.util.Map;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2024年12月23日
 * @version: 1.0
 */
@Data
public class IntelligentDeviceAddBO {

    /**
     * 产品标识
     */
    private String productKey;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 分组编号
     */
    private String groupNo;

    /**
     * 产品型号标识
     */
    private String productModelNo;

    /**
     * 设备别名
     */
    private String remarkName;

    /**
     * 设备标签
     */
    private Map<String, String> tag;
}
