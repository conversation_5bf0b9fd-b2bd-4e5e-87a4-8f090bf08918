/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.k2.management.domain.vo.station;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import com.jdx.k2.management.domain.vo.deploy.DeploymentPlanVO;
import com.jdx.k2.management.domain.vo.station.warehouse.GroupBusinessVO;
import lombok.Data;
import java.util.List;

/**
 * 运营管理系统站点管理新增站点对象
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class StationAddVO {

    /**
     * 所在城市
     */
    @NotNull(message = "所在城市不能为空")
    private Integer cityId;

    /**
     * 站点名称
     */
    @NotBlank(message = "站点名称不能为空")
    private String name;

    /**
     * 站点用途
     */
    @NotBlank(message = "站点用途不能为空")
    private String useCase;

    /**
     * 站点运营方类型
     */
    @NotBlank(message = "站点运营方类型不能为空")
    private String type;

    /**
     * 站点类型
     */
    @NotBlank(message = "站点类型不能为空")
    private String productType;

    /**
     * 负责人姓名
     */
    @NotBlank(message = "负责人姓名不能为空")
    private String personName;

    /**
     * 负责人电话
     */
    @NotBlank(message = "负责人电话不能为空")
    private String contact;

    /**
     * 经度
     */
    @NotNull(message = "经度不能为空")
    private Double lon;

    /**
     * 纬度
     */
    @NotNull(message = "纬度不能为空")
    private Double lat;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 无人车站点青龙系统编号
     */
    private String number;

    /**
     * 无人车站点揽件接单最小时长(min)
     */
    private Integer minDropOffTime;

    /**
     * 自动回充电量
     */
    private Double autoChargeLimit;

    /**
     * 可接任务电量
     */
    private Double missionChargeLimit;

    /**
     * 强制回充电量
     */
    private Double forceChargeLimit;

    /**
     * 车辆亮灯提醒时间（单位：分钟）
     */
    private Integer reminderTime;

    /**
     * 充电方式，charge为充电，swap为换电
     */
    private String chargeType;

    /**
     * 业务列表
     */
    private List<GroupBusinessVO> groupBusinessList;

    /**
     * 部署计划信息
     */
    private DeploymentPlanVO deploymentPlanInfo;
}
