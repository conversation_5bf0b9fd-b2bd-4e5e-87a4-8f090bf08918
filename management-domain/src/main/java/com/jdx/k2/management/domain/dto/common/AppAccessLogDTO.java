package com.jdx.k2.management.domain.dto.common;

import java.io.Serializable;
import java.util.Date;
import com.jd.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * 系统访问日志-返回对象
 *
 * <AUTHOR>
 */
@Data
public class AppAccessLogDTO implements Serializable {

    private static final long serialVersionUID = -8692506368702838352L;

    /**
     * 唯一标识
     */
    private Integer id;

    /**
     * 操作描述
     */
    private String description;

    /**
     * 请求参数
     */
    private String requestParam;

    /**
     * 返回结果
     */
    private String returnResult;

    /**
     * 异常名称
     */
    private String exceptionName;

    /**
     * 异常信息
     */
    private String exceptionMessage;

    /**
     * 操作人员
     */
    private String operationUser;

    /**
     * 方法
     */
    private String method;

    /**
     * 请求URI
     */
    private String uri;

    /**
     * 请求IP
     */
    private String ip;

    /**
     * 创建时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 应用服务
     */
    private String appName;

    /**
     * 功能模块
     */
    private String moduleName;

    /**
     * 操作类型
     */
    private String operationTypeName;

    /**
     * 是否正常
     */
    private String flagName;
}
