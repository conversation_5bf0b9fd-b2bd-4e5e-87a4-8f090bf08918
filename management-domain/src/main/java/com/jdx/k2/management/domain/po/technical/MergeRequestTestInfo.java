/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.k2.management.domain.po.technical;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jdx.k2.management.domain.base.BaseDomain;
import java.util.Date;
import lombok.Data;

/**
 * 集成测试管理
 *
 * <AUTHOR>
 */
@Data
@TableName("merge_request_test_info")
public class MergeRequestTestInfo extends BaseDomain {

    /**
     * 合并请求ID
     */
    private Integer mrId;

    /**
     * 合并请求序号
     */
    private Integer mrNumber;

    /**
     * 合入分支
     */
    private String mrBranch;

    /**
     * 代码仓库ID
     */
    private Integer repoId;

    /**
     * 行云卡片编号
     */
    private String jagileCardCode;

    /**
     * 合并请求更新时间
     */
    private Date mrUpdateTime;

    /**
     * 是否提测
     */
    private Integer isSubmitTest;

    /**
     * 不提测原因
     */
    private String noTestReason;

    /**
     * fix_jira
     */
    private String fixJiraKeys;

    /**
     * 合并请求所属模块
     */
    private String module;

    /**
     * 合并请求修改内容
     */
    private String description;

    /**
     * 合并请求所需依赖
     */
    private String dependency;

    /**
     * 期望结果
     */
    private String expectedOutcome;

    /**
     * 开发人员
     */
    private String developer;

    /**
     * 测试前提条件
     */
    private String precondition;

    /**
     * 操作步骤
     */
    private String operationStep;

    /**
     * 实际结果
     */
    private String actualResult;

    /**
     * 测试人员
     */
    private String tester;

    /**
     * bug_jira
     */
    private String bugJiraKeys;

    /**
     * 测试结果
     */
    private Integer testResult;

    /**
     * 是否同步JIRA
     */
    private Integer isSyncJira;

    /**
     * 测试备注
     */
    private String remark;

    /**
     * MR测试审核人员
     */
    private String testReviewer;

    /**
     * 测试通过时间
     */
    private Date testPassTime;

    /**
     * MR发版时间
     */
    private Date mrReleaseTime;

    /**
     * 发版版本号
     */
    private String roverVersion;

    /**
     * 亮灯颜色
     */
    private String alarmColor;

    /**
     * 亮灯天数
     */
    private Integer alarmDays;

    /**
     * 预计测试完成时间
     */
    private Date estimatedTestTime;

    /**
     * 行云缺陷
     */
    private String xingyunJiraKeys;

    /**
     * MR关注状态
     */
    private String mrStatus;

    /**
     * 已测试天数
     */
    private Integer testedDays;
}
