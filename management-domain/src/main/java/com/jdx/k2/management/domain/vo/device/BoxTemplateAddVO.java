/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.k2.management.domain.vo.device;

import java.util.List;
import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * 车辆管理系统新增箱体模板请求对象
 *
 * <AUTHOR> wangguotai
 * @version 1.0
 */
@Data
public class BoxTemplateAddVO {

    /**
     * 模板名称
     */
    @NotBlank(message = "模板名称不能为空")
    private String name;

    /**
     * 硬件型号ID
     */
    @NotNull(message = "硬件型号ID不能为空")
    private Integer hardwareModelId;

    /**
     * 驱动类型
     */
    @NotBlank(message = "驱动类型不能为空")
    private String driverType;

    /**
     * 串口号
     */
    @NotBlank(message = "串口号不能为空")
    private String deviceId;

    /**
     * 波特率
     */
    @NotNull(message = "波特率不能为空")
    private Integer baudRate;

    /**
     * 所属产品
     */
    @NotBlank(message = "所属产品不能为空")
    private String productType;

    /**
     * 左侧货箱列数
     */
    @Min(value = 0, message = "左侧货箱列数最小值为0")
    @Max(value = 9, message = "左侧货箱列数最大值为9")
    private Integer leftBoxColumnNum;

    /**
     * 右侧货箱列数
     */
    @Min(value = 0, message = "右侧货箱列数最小值为0")
    @Max(value = 9, message = "右侧货箱列数最大值为9")
    private Integer rightBoxColumnNum;

    /**
     * 是否生效
     */
    @NotNull(message = "是否生效不能为空")
    @Min(value = 0, message = "是否生效最小值为0")
    @Max(value = 1, message = "是否生效最大值为1")
    private Integer enable;

    /**
     * 箱体格口模板列表
     */
    @NotEmpty(message = "箱体格口模板列表不能为空")
    @Valid
    private List<BoxGridTemplateVO> gridList;
}