package com.jdx.k2.management.domain.po.other;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jdx.k2.management.domain.base.BaseDomain;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 角色关联资源 数据库表名为role_resource
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = false)
@Data
@TableName("role_resource")
public class RoleResource extends BaseDomain {

    /**
     * 角色编号
     */
    private String roleNumber;

    /**
     * 资源编号
     */
    private String resourceNumber;
}