package com.jdx.k2.management.domain.dto.station.warehouse;

import lombok.Data;

/**
 * @description: 多合一下的管控区信息列表传输对象
 * @author: douy<PERSON><PERSON>
 * @date: 2024/6/4
 */
@Data
public class WarehouseZoneInfoListDTO {

    /**
     * 区域编号
     */
    private String zoneNo;

    /**
     * 区域名称
     */
    private String zoneName;

    /**
     * 区域类型
     */
    private String type;

    /**
     * 区域类型名称
     */
    private String typeName;

    /**
     * 地图名称
     */
    private String mapName;

    /**
     * 所属地图编号
     */
    private String mapNumber;

    /**
     * 所属地图版本编号
     */
    private String mapVersionNumber;

    /**
     * 所属地图版本号
     */
    private String mapVersion;

    /**
     * 占用的设备名称
     */
    private String useDeviceName;

    /**
     * 启用状态
     */
    private Integer enable;

    /**
     * 启用状态名称
     */
    private String enableName;

    /**
     * 使用状态
     */
    private String useStatus;

    /**
     * 使用状态名称
     */
    private String useStatusName;
}
