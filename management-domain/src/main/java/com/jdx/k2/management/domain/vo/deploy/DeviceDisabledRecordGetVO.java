/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.k2.management.domain.vo.deploy;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <p>获取车辆异常填报详情信息VO
 *
 * @Since: 1.0.0
 * @Author: jiangzesheng1
 * @Date: 2025-02-27 10:23
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DeviceDisabledRecordGetVO {

    /**
     * 记录ID
     */
    @NotNull(message = "记录ID不能为空")
    private Integer recordId;
}
