package com.jdx.k2.management.domain.po.common;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jdx.k2.management.domain.base.BaseDomain;
import lombok.Data;

/**
 * 上传结果
 */
@Data
@TableName("upload_result")
public class UploadResult extends BaseDomain {

    /**
     * 文件名称
     */
    private String name;

    /**
     * 上传类型
     */
    private Integer uploadType;

    /**
     * 上传数量
     */
    private Integer totalCount;

    /**
     * 成功个数
     */
    private Integer successCount;

    /**
     * 失败个数
     */
    private Integer failCount;

    /**
     * 结果描述
     */
    private String resultDescription;
}
