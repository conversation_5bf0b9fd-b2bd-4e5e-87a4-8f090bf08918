package com.jdx.k2.management.domain.po.station.robot;

import com.jdx.k2.management.domain.base.BaseDomain;
import lombok.Data;
import java.util.Date;

/**
 * <AUTHOR>
 * @description: 机器人运营部信息表
 * @date 2024年03月01日
 * @version: 1.0
 */
@Data
public class GroupInfo extends BaseDomain {


    /**
     * station_base_info表id
     */
    private Integer stationBaseId;

    /**
     * 自动回充电量
     */
    private Double autoChargeLimit;

    /**
     * 可接任务电量
     */
    private Double missionChargeLimit;

    /**
     * 强制回充电量
     */
    private Double forceChargeLimit;

    /**
     * 车端亮灯波次提醒分钟数
     */
    private Integer reminderTime;

    /**
     * AMR充电方式
     */
    private String chargeType;
}
