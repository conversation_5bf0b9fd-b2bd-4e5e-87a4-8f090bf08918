package com.jdx.k2.management.domain.dto.station.warehouse;

import lombok.Data;

/**
 * @description: 站点下的点位信息列表传输对象
 * @author: do<PERSON><PERSON><PERSON>
 * @date: 2024/6/4
 */
@Data
public class WarehousePointInfoListDTO {

    /**
     * 点位编号
     */
    private String pointNo;

    /**
     * 点位名称
     */
    private String pointName;

    /**
     * 点位类型
     */
    private String pointType;

    /**
     * 点位类型名称
     */
    private String pointTypeName;

    /**
     * 所属地图名称
     */
    private String mapName;

    /**
     * 所属地图编号
     */
    private String mapNumber;

    /**
     * 所属地图版本编号
     */
    private String mapVersionNumber;

    /**
     * 所属地图版本号
     */
    private String mapVersion;

    /**
     * 所属停靠站名称
     */
    private String stopStationName;

    /**
     * 绑定的设备名称
     */
    private String bindDeviceName;

    /**
     * 占用的设备名称
     */
    private String useDeviceName;

    /**
     * 停启用状态
     */
    private Integer enable;

    /**
     * 停启用状态名称
     */
    private String enableName;

    /**
     * 使用状态
     */
    private String useStatus;

    /**
     * 使用状态名称
     */
    private String useStatusName;
}
