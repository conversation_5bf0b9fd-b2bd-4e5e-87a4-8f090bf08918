package com.jdx.k2.management.domain.vo.technical.bug;

import com.jdx.rover.common.domain.page.PageVO;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class BugPageListVO extends PageVO {
    /**
     * 用户体验功能局限标签
     */
    private List<String> ueLimitTagList;

    /**
     * 是否需要仿真回归
     * 0:不需要 1:需要
     */
    private Integer isSimulation;

    /**
     * 亮灯颜色
     */
    private List<String> alarmColors;

    /**
     * 缺陷编号
     */
    private String bugCode;

    /**
     * 优先级
     */
    private String bugPriority;

    /**
     * 缺陷状态
     */
    private List<String> bugStatusList;

    /**
     * 缺陷创建开始日期
     */
    private Date bugCreateStartDate;

    /**
     * 缺陷创建结束日期
     */
    private Date bugCreateEndDate;

    /**
     * 版本号
     */
    private String roverVersion;

    /**
     * 问题标签
     */
    private List<Integer> labelIds;

    /**
     * 是否有效
     */
    private Integer enable;

    /**
     * 缺陷提交人
     */
    private String bugProposer;

    /**
     * 缺陷受理人
     */
    private String bugAssignee;

    /**
     * 车型列表
     */
    private List<Integer> deviceTypeBaseIdList;

    /**
     * 是否质控组提交缺陷
     */
    private Boolean isQaGroupDefectSubmitted;

    /**
     * 功能场景列表
     */
    private List<String> functionSceneList;

    /**
     * 障碍物信息列表
     */
    private List<String> obstacleInfoList;

    /**
     * 障碍物方位列表
     */
    private List<String> obstaclePositionList;

    /**
     * 障碍物意图列表
     */
    private List<String> obstacleIntentList;

    /**
     * 道路结构列表
     */
    private List<String> roadStructureList;
}
