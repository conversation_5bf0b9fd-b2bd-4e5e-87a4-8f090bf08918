/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.k2.management.domain.dto.device;

import java.util.Date;
import java.util.List;
import com.jd.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.ToString;

/**
 * 传感器方案数据传输对象
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
@ToString(callSuper = true)
public class SensorSchemeDTO {

    /**
     * 主键Id
     */
    private Integer id;

    /**
     * 名称
     */
    private String name;

    /**
     * 所属产品
     */
    private String productType;

    /**
     * 所属产品名称
     */
    private String productTypeName;

    /**
     * 传感器方案明细列表
     */
    private List<SensorSchemeDetailDTO> sensorSchemeDetailList;

    /**
     * 创建时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date modifyTime;

    /**
     * 最后修改者
     */
    private String modifyUser;

    /**
     * 最后修改者ID
     */
    private Integer modifyUserId;

    /**
     * 删除标志
     */
    private Integer deleted;

    /**
     * 是否有效
     */
    private Integer enable;


    /**
     * 是否有效正文名称
     */
    private String enableName;
}
