package com.jdx.k2.management.domain.po.station.vehicle;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jdx.k2.management.domain.base.BaseDomain;
import lombok.Data;

/**
 * 停靠点关键字
 *
 * <AUTHOR>
 */
@Data
@TableName("stop_keyword")
public class StopKeyword extends BaseDomain {

    /**
     * 停靠点Id
     */
    private Integer stopId;

    /**
     * 站点Id
     */
    private Integer stationId;

    /**
     * 关键字
     */
    private String stopKeyword;
}