/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.k2.management.domain.po.technical;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jdx.k2.management.domain.base.BaseDomain;
import lombok.Data;

import java.util.Date;

/**
 * 缺陷状态变化增量表，只记录各个状态新增的缺陷
 *
 * <AUTHOR>
 */
@Data
@TableName("bug_state_change_add_info")
public class BugStateChangeAddInfo extends BaseDomain {

    /**
     * 记录日期
     */
    private Date recordDate;

    /**
     * 缺陷状态
     */
    private String bugStatus;

    /**
     * 缺陷编号
     */
    private String bugCode;

    /**
     * 是否为总数新增的缺陷(0:否 1:是)
     */
    private Integer realAdded;
}
