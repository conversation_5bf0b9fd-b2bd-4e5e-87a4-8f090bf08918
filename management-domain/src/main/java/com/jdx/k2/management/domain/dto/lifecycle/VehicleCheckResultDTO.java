/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.k2.management.domain.dto.lifecycle;

import java.util.Date;
import com.jd.fastjson.annotation.JSONField;
import lombok.Data;

/**
 
 * 运营管理系统查看标定结果数据传输对象
 
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class VehicleCheckResultDTO {

    /**
     * 标定结果ID标识
     */
    private Integer id;

    /**
     * 最近维护人
     */
    private String modifyUser;

    /**
     * 最近维护时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date modifyTime;
}